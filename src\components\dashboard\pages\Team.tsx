'use client';

import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { ShieldCheck, Check, Clock, RefreshCw, Users, Mail, Settings } from 'lucide-react';
import { updateUserActiveStatus } from '@/lib/actions/team';
import TeamMembersList from './team/TeamMembersList';
import PendingInvitesList from './team/PendingInvitesList';
import RolesInfo from './team/RolesInfo';
import InviteForm from './team/InviteForm';
import { toast } from 'sonner';
import { UserRole } from '@prisma/client';
import { Button } from '@/components/ui/button';
import OrganizationSetup from './team/OrganizationSetup';
import {
  isClerkInvitationsEnabled,
  validateTeamConfig,
} from '@/lib/team-config';

// Define types for props
interface TeamMember {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  profileImageUrl: string | null;
  role: UserRole;
  lastActive: Date | null;
  status: string;
  createdAt: Date;
}

interface Invite {
  id: string;
  email: string;
  role: UserRole;
  invitedBy: string;
  invitedOn: string;
  expiresAt: Date;
}

interface Statistics {
  totalMembers: number;
  activeMembers: number;
  pendingInvites: number;
  roleDistribution: Record<string, number>;
}

interface TeamProps {
  members: TeamMember[];
  invites: Invite[];
  statistics: Statistics;
  initialErrors: {
    members?: string;
    invites?: string;
    statistics?: string;
  };
}

const Team = ({
  members: initialMembers = [],
  invites: initialInvites = [],
  statistics: initialStatistics = {
    totalMembers: 0,
    activeMembers: 0,
    pendingInvites: 0,
    roleDistribution: {},
  },
  initialErrors = {},
}: TeamProps) => {
  // State
  const [members] = useState(initialMembers);
  const [invites] = useState(initialInvites);
  const [statistics] = useState(initialStatistics);
  const [activeTab, setActiveTab] = useState('members');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Configuration: Controlled by environment variable
  // Set NEXT_PUBLIC_USE_CLERK_INVITATIONS=true in .env to enable Clerk's invitation system
  // Set NEXT_PUBLIC_USE_CLERK_INVITATIONS=false or leave unset to use custom invitations
  const USE_CLERK_INVITATIONS = isClerkInvitationsEnabled();

  // Validate configuration and show warnings if needed
  React.useEffect(() => {
    const configValidation = validateTeamConfig();
    if (!configValidation.isValid) {
      configValidation.errors.forEach((error) => {
        toast.error(`Configuration Error: ${error}`);
      });
    }
    configValidation.warnings.forEach((warning) => {
      toast.warning(`Configuration Warning: ${warning}`);
    });
  }, []);

  // Show errors as toasts
  useEffect(() => {
    if (initialErrors.members) {
      toast.error(`Error loading members: ${initialErrors.members}`);
    }
    if (initialErrors.invites) {
      toast.error(`Error loading invites: ${initialErrors.invites}`);
    }
    if (initialErrors.statistics) {
      toast.error(
        `Error loading statistics: ${initialErrors.statistics}`
      );
    }
  }, [initialErrors]);

  // Update user activity status
  useEffect(() => {
    updateUserActiveStatus().catch(() => {});
  }, []);

  // Refresh all data
  const refreshData = async () => {
    setIsRefreshing(true);

    try {
      // In a real implementation, you would fetch updated data here
      // For this example, we'll just simulate a delay
      await new Promise((resolve) => setTimeout(resolve, 500));
      
      // Trigger a re-render by updating state
      // This will cause the components to re-fetch their data
      toast.success('Data refreshed successfully');
    } catch {
      toast.error('Failed to refresh data');
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen flex-col w-full">
        <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
          {/* Enhanced Header */}
          <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <div className="space-y-1">
              <h2 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 dark:from-white dark:via-blue-200 dark:to-indigo-200 bg-clip-text text-transparent">
                Team Management
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                Manage team members, roles, and access permissions
                {isRefreshing && (
                  <span className="ml-2 animate-pulse text-purple-600 dark:text-purple-400">
                    Refreshing...
                  </span>
                )}
              </p>
              {USE_CLERK_INVITATIONS && (
                <div className="mt-3 p-3 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 border border-blue-200/50 dark:border-blue-700/50 rounded-lg">
                  <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                    <strong>Clerk Invitations Enabled:</strong> Using
                    Clerk&apos;s built-in invitation system. If you encounter
                    issues, you may need to create a Clerk organization
                    first.
                  </p>
                </div>
              )}
            </div>
            
            {/* Enhanced Controls Section */}
            <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
              <Button
                onClick={refreshData}
                disabled={isRefreshing}
                className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0"
              >
                <RefreshCw
                  className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}
                />
                Refresh
              </Button>
              <InviteForm
                onSuccess={refreshData}
                useClerkInvitations={USE_CLERK_INVITATIONS}
              />
            </div>
          </div>

          {/* Organization Setup Component */}
          <div className="mb-6">
            <OrganizationSetup onSetupComplete={refreshData} />
          </div>

          {/* Enhanced Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10">
              <CardHeader className="pb-3 border-b border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-r from-blue-50/50 via-white to-cyan-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20 rounded-t-3xl">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-semibold text-gray-900 dark:text-gray-100">
                    Total Members
                  </CardTitle>
                  <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 flex items-center justify-center shadow-sm">
                    <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  {statistics.totalMembers}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 font-medium">
                  Across all roles
                </p>
              </CardContent>
            </Card>

            <Card className="border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-br from-white via-teal-50/20 to-emerald-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-emerald-950/10">
              <CardHeader className="pb-3 border-b border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-r from-teal-50/50 via-white to-emerald-50/50 dark:from-teal-950/20 dark:via-gray-800/50 dark:to-emerald-950/20 rounded-t-3xl">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-semibold text-gray-900 dark:text-gray-100">
                    Active Members
                  </CardTitle>
                  <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-teal-100 to-emerald-100 dark:from-teal-900/40 dark:to-emerald-900/40 flex items-center justify-center shadow-sm">
                    <Check className="h-5 w-5 text-teal-600 dark:text-teal-400" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  {statistics.activeMembers}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 font-medium">
                  Active in the last 24 hours
                </p>
              </CardContent>
            </Card>

            <Card className="border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10">
              <CardHeader className="pb-3 border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/50 via-white to-indigo-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 rounded-t-3xl">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base font-semibold text-gray-900 dark:text-gray-100">
                    Pending Invites
                  </CardTitle>
                  <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40 flex items-center justify-center shadow-sm">
                    <Mail className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  {statistics.pendingInvites}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 font-medium">
                  Awaiting acceptance
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Tabs Section */}
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-6"
          >
            <div className="flex items-center justify-between">
              <TabsList className="grid w-fit grid-cols-3 bg-gradient-to-r from-gray-50 via-blue-50/50 to-cyan-50/50 dark:from-gray-800/50 dark:via-blue-950/30 dark:to-cyan-950/30 border border-gray-200/50 dark:border-gray-700/50 shadow-lg backdrop-blur-sm">
                <TabsTrigger
                  value="members"
                  className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-all duration-300"
                >
                  <ShieldCheck className="w-4 h-4" />
                  Team Members
                </TabsTrigger>
                <TabsTrigger
                  value="invites"
                  className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-all duration-300"
                >
                  <Mail className="w-4 h-4" />
                  Pending Invites
                </TabsTrigger>
                <TabsTrigger
                  value="roles"
                  className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-teal-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-all duration-300"
                >
                  <Settings className="w-4 h-4" />
                  Roles & Permissions
                </TabsTrigger>
              </TabsList>

              {/* Tab Info */}
              <div className="hidden md:block text-sm text-gray-500 dark:text-gray-400 font-medium">
                {activeTab === 'members' && `${statistics.totalMembers} total members`}
                {activeTab === 'invites' && `${statistics.pendingInvites} pending invites`}
                {activeTab === 'roles' && 'Role management'}
              </div>
            </div>

            <TabsContent value="members" className="space-y-4">
              <Card className="border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-br from-white via-blue-50/10 to-cyan-50/10 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10">
                <CardContent className="p-0">
                  <TeamMembersList
                    members={members}
                    onRefresh={refreshData}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="invites" className="space-y-4">
              <Card className="border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-br from-white via-purple-50/10 to-indigo-50/10 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10">
                <CardContent className="p-0">
                  <PendingInvitesList
                    invites={invites}
                    onRefresh={refreshData}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="roles" className="space-y-4">
              <Card className="border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-br from-white via-teal-50/10 to-emerald-50/10 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-emerald-950/10">
                <CardContent className="p-0">
                  <RolesInfo />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Team;

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Building,
  Mail,
  CheckCircle,
  XCircle,
  AlertCircle,
  Settings,
  ArrowRight,
  Shield,
  Users,
} from 'lucide-react';
import { useOrganization } from '@clerk/nextjs';
import { toast } from 'sonner';

interface OrganizationSetupProps {
  onSetupComplete?: () => void;
}

interface LocalOrganization {
  id: string;
  name: string;
}

export default function OrganizationSetup({
  onSetupComplete,
}: OrganizationSetupProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [orgName, setOrgName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [activeStep, setActiveStep] = useState<
    'choose' | 'create' | 'custom'
  >('choose');
  const [localOrganizations, setLocalOrganizations] = useState<LocalOrganization[]>([]);

  const { organization: clerkOrganization, isLoaded: orgLoaded } = useOrganization();

  // Check for both Clerk organization and local organizations
  const hasClerkOrganization = orgLoaded && !!clerkOrganization;
  const hasLocalOrganizations = localOrganizations.length > 0;
  const hasAnyOrganization = hasClerkOrganization || hasLocalOrganizations;

  // Get organization names for display
  const getOrganizationNames = () => {
    const names: string[] = [];
    if (hasClerkOrganization && clerkOrganization?.name) {
      names.push(clerkOrganization.name);
    }
    if (hasLocalOrganizations) {
      names.push(...localOrganizations.map(org => org.name));
    }
    return names;
  };

  // Load all local organizations on component mount and after creation
  const loadLocalOrganizations = async () => {
    try {
      const response = await fetch('/api/organizations/current');
      if (response.ok) {
        const data = await response.json();
        if (data.organizations) {
          setLocalOrganizations(data.organizations);
        }
      }
    } catch (error) {
      console.error('Error loading organizations:', error);
    }
  };

  useEffect(() => {
    loadLocalOrganizations();
  }, []);

  const handleCreateClerkOrg = async () => {
    if (!orgName.trim()) {
      toast.error('Please enter an organization name');
      return;
    }

    setIsCreating(true);
    try {
      const response = await fetch('/api/organizations/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: orgName.trim() }),
      });

      if (response.ok) {
        toast.success('Organization created successfully!');
        setIsOpen(false);
        setOrgName('');
        await loadLocalOrganizations();
        onSetupComplete?.();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create organization');
      }
    } catch (error) {
      console.error('Error creating organization:', error);
      toast.error('Failed to create organization');
    } finally {
      setIsCreating(false);
    }
  };

  const handleUseCustomSystem = () => {
    setIsOpen(false);
    onSetupComplete?.();
    toast.info('Switched to custom invitation system');
  };

  return (
    <>
      {/* Enhanced Status Cards */}
      <div className="grid gap-4 md:grid-cols-2 mb-6">
        <Card className="border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 border-b border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-r from-blue-50/50 via-white to-cyan-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20 rounded-t-3xl">
            <CardTitle className="text-base font-semibold text-gray-900 dark:text-gray-100">
              Organization
            </CardTitle>
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 flex items-center justify-center shadow-sm">
              <Building className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="flex flex-col space-y-2">
              {hasAnyOrganization ? (
                getOrganizationNames().map((name, idx) => (
                  <div key={idx} className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-700 dark:text-green-400 font-medium">{name}</span>
                  </div>
                ))
              ) : (
                <div className="flex items-center space-x-2">
                  <XCircle className="h-4 w-4 text-red-500" />
                  <span className="text-sm text-red-700 dark:text-red-400 font-medium">Not set up</span>
                </div>
              )}
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-2 font-medium">
              Required for team management
            </p>
          </CardContent>
        </Card>

        <Card className="border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/50 via-white to-indigo-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 rounded-t-3xl">
            <CardTitle className="text-base font-semibold text-gray-900 dark:text-gray-100">
              Invitation System
            </CardTitle>
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40 flex items-center justify-center shadow-sm">
              <Mail className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm text-green-700 dark:text-green-400 font-medium">
                Custom system active
              </span>
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-2 font-medium">
              Always available as fallback
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Setup Dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0">
            <Settings className="mr-2 h-4 w-4" />
            Configure Team Invitations
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px] bg-gradient-to-br from-white via-blue-50/10 to-cyan-50/10 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10 border border-blue-200/30 dark:border-blue-700/20">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Team Invitation Setup</DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-400">
              Choose how you want to handle team member invitations
            </DialogDescription>
          </DialogHeader>

          {activeStep === 'choose' && (
            <div className="space-y-4">
              <div className="grid gap-4">
                <Card
                  className="cursor-pointer hover:bg-blue-50/50 dark:hover:bg-blue-950/20 transition-all duration-200 border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10"
                  onClick={() => setActiveStep('create')}
                >
                  <CardHeader className="border-b border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-r from-blue-50/50 via-white to-cyan-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20 rounded-t-3xl">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 rounded-xl bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 shadow-sm">
                          <Building className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <CardTitle className="text-lg font-bold text-gray-900 dark:text-gray-100">
                          Create Organization
                        </CardTitle>
                      </div>
                      <Badge className="bg-gradient-to-r from-blue-500 to-cyan-600 text-white border-0">Recommended</Badge>
                    </div>
                    <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                      Create an organization for team management
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <ul className="text-sm space-y-2 text-gray-600 dark:text-gray-400">
                      <li className="flex items-center">
                        <Users className="mr-2 h-4 w-4 text-blue-500" />
                        Team member management
                      </li>
                      <li className="flex items-center">
                        <Shield className="mr-2 h-4 w-4 text-blue-500" />
                        Role-based access control
                      </li>
                      <li className="flex items-center">
                        <Mail className="mr-2 h-4 w-4 text-blue-500" />
                        Invitation system
                      </li>
                      <li className="flex items-center">
                        <Settings className="mr-2 h-4 w-4 text-blue-500" />
                        Organization-wide settings
                      </li>
                    </ul>
                    <div className="mt-4 flex items-center text-sm text-blue-600 dark:text-blue-400 font-medium">
                      <span>Setup required</span>
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </div>
                  </CardContent>
                </Card>

                <Card
                  className="cursor-pointer hover:bg-purple-50/50 dark:hover:bg-purple-950/20 transition-all duration-200 border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10"
                  onClick={() => setActiveStep('custom')}
                >
                  <CardHeader className="border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/50 via-white to-indigo-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 rounded-t-3xl">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 rounded-xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40 shadow-sm">
                        <Mail className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                      </div>
                      <CardTitle className="text-lg font-bold text-gray-900 dark:text-gray-100">
                        Custom Email System
                      </CardTitle>
                    </div>
                    <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                      Use the existing custom invitation system
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <ul className="text-sm space-y-2 text-gray-600 dark:text-gray-400">
                      <li className="flex items-center">
                        <Mail className="mr-2 h-4 w-4 text-purple-500" />
                        Full control over email templates
                      </li>
                      <li className="flex items-center">
                        <Settings className="mr-2 h-4 w-4 text-purple-500" />
                        Custom invitation flow
                      </li>
                      <li className="flex items-center">
                        <Shield className="mr-2 h-4 w-4 text-purple-500" />
                        Works with any email provider
                      </li>
                      <li className="flex items-center">
                        <CheckCircle className="mr-2 h-4 w-4 text-purple-500" />
                        No additional setup needed
                      </li>
                    </ul>
                    <div className="mt-4 flex items-center text-sm text-green-600 dark:text-green-400 font-medium">
                      <CheckCircle className="mr-2 h-4 w-4" />
                      <span>Ready to use</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeStep === 'create' && (
            <div className="space-y-4">
              <Alert className="border-blue-200/50 dark:border-blue-700/50 bg-blue-50/50 dark:bg-blue-950/30">
                <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <AlertDescription className="text-blue-700 dark:text-blue-300">
                  Creating an organization will enable team management features
                </AlertDescription>
              </Alert>

              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="orgName" className="text-gray-900 dark:text-gray-100 font-medium">Organization Name</Label>
                  <Input
                    id="orgName"
                    placeholder="My Company"
                    value={orgName}
                    onChange={(e) => setOrgName(e.target.value)}
                    className="border-blue-200/50 dark:border-blue-700/50 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500/20"
                  />
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    This will be visible to all team members
                  </p>
                </div>
              </div>

              <DialogFooter className="gap-2">
                <Button
                  variant="outline"
                  onClick={() => setActiveStep('choose')}
                  className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Back
                </Button>
                <Button
                  onClick={handleCreateClerkOrg}
                  disabled={isCreating || !orgName.trim()}
                  className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0"
                >
                  {isCreating ? 'Creating...' : 'Create Organization'}
                </Button>
              </DialogFooter>
            </div>
          )}

          {activeStep === 'custom' && (
            <div className="space-y-4">
              <Alert className="border-green-200/50 dark:border-green-700/50 bg-green-50/50 dark:bg-green-950/30">
                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                <AlertDescription className="text-green-700 dark:text-green-300">
                  The custom invitation system is ready to use. Team
                  invitations will be sent via email.
                </AlertDescription>
              </Alert>

              <div className="space-y-3">
                <h4 className="font-medium text-gray-900 dark:text-gray-100">What you get:</h4>
                <ul className="text-sm space-y-2 text-gray-600 dark:text-gray-400">
                  <li className="flex items-center">
                    <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                    Email invitations with secure tokens
                  </li>
                  <li className="flex items-center">
                    <Shield className="mr-2 h-4 w-4 text-green-500" />
                    Role-based access control
                  </li>
                  <li className="flex items-center">
                    <Settings className="mr-2 h-4 w-4 text-green-500" />
                    Invitation management dashboard
                  </li>
                  <li className="flex items-center">
                    <Mail className="mr-2 h-4 w-4 text-green-500" />
                    7-day invitation expiry
                  </li>
                </ul>
              </div>

              <DialogFooter className="gap-2">
                <Button
                  variant="outline"
                  onClick={() => setActiveStep('choose')}
                  className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Back
                </Button>
                <Button 
                  onClick={handleUseCustomSystem}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0"
                >
                  Use Custom System
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}

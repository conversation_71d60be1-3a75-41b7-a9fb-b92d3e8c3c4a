import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import Colors from '../theme/Colors'

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 cursor-pointer",
  {
    variants: {
      variant: {
        default: `bg-[${Colors.primary}] text-[${Colors.textLight}] hover:bg-[${Colors.primaryDark}] shadow-sm hover:shadow-md transform hover:scale-[1.02]`,
        destructive:
          `bg-[${Colors.error}] text-[${Colors.textLight}] hover:bg-[${Colors.error}]/90 shadow-sm hover:shadow-md transform hover:scale-[1.02]`,
        outline:
          `border border-[${Colors.border}] bg-[${Colors.background}] hover:bg-[${Colors.accent}] hover:text-[${Colors.textLight}] shadow-sm hover:shadow-md transform hover:scale-[1.02]`,
        secondary:
          `bg-[${Colors.accent}] text-[${Colors.textLight}] hover:bg-[${Colors.accentDark}] shadow-sm hover:shadow-md transform hover:scale-[1.02]`,
        subtle: `bg-[${Colors.background}] text-[${Colors.text}] border border-[${Colors.border}] hover:bg-[${Colors.accent}] hover:text-[${Colors.textLight}] hover:border-[${Colors.accent}] shadow-sm hover:shadow-md transform hover:scale-[1.02]`,
        link: `text-[${Colors.primary}] underline-offset-4 hover:underline bg-transparent hover:bg-transparent`,
        primaryGradient: `bg-gradient-to-r from-[${Colors.primary}] to-[${Colors.primaryDark}] text-[${Colors.textLight}] hover:from-[${Colors.accent}] hover:to-[${Colors.primaryDark}] shadow-lg hover:shadow-xl transform hover:scale-[1.02]`,
        secondaryGradient: `bg-gradient-to-r from-[${Colors.primary}]/10 to-[${Colors.primaryDark}]/10 text-[${Colors.primary}] border border-[${Colors.primary}]/20 hover:from-[${Colors.primary}]/20 hover:to-[${Colors.primaryDark}]/20 shadow-sm hover:shadow-md transform hover:scale-[1.02]`,
        blueGlow: `relative bg-gradient-to-r from-[${Colors.primary}] to-[${Colors.primaryDark}] text-[${Colors.textLight}] before:absolute before:inset-0 before:-z-10 before:rounded-md before:bg-gradient-to-r before:from-[${Colors.primary}] before:to-[${Colors.primaryDark}] before:blur-lg before:opacity-70 before:transition-opacity hover:before:opacity-100 shadow-lg hover:shadow-xl transform hover:scale-[1.02]`,
        success: `bg-[${Colors.success}] text-[${Colors.textLight}] hover:bg-[${Colors.success}]/90 shadow-sm hover:shadow-md transform hover:scale-[1.02]`,
        warning: `bg-[${Colors.warning}] text-[${Colors.text}] hover:bg-[${Colors.warning}]/90 shadow-sm hover:shadow-md transform hover:scale-[1.02]`,
        info: `bg-[${Colors.info}] text-[${Colors.textLight}] hover:bg-[${Colors.info}]/90 shadow-sm hover:shadow-md transform hover:scale-[1.02]`,
        soft: `bg-[${Colors.background}] text-[${Colors.text}] border border-[${Colors.border}] hover:bg-[${Colors.primary}]/5 hover:border-[${Colors.primary}]/20 shadow-sm hover:shadow-md`,
        glass: `bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 shadow-sm hover:shadow-md`,
        ghost: `bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 shadow-sm hover:shadow-md transform hover:scale-[1.02]`,
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "blueGlow",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }

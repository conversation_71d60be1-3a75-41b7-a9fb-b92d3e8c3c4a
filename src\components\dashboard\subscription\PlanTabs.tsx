'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from '@/components/ui/tabs';
import { Calendar, Clock } from 'lucide-react';
import PlanGrid from './PlanGrid';

interface Plan {
  id: number;
  name: string;
  description: string | null;
  price: string;
  interval: string | null;
  intervalCount: number | null;
  isUsageBased: boolean | null;
  productId: number;
  productName: string | null;
  paddlePriceId: string | null;
  stripeProductId?: string | null;
  stripePriceId?: string | null;
  features?: string | null;
  sort: number | null;
}

interface PlanTabsProps {
  plans: Plan[];
  currentPlan: Plan | null | undefined;
  formatPrice: (price: string) => string;
  handlePlanUpgrade: (priceId: string) => void;
  isLoading: boolean;
}

const PlanTabs: React.FC<PlanTabsProps> = ({
  plans,
  currentPlan,
  formatPrice,
  handlePlanUpgrade,
  isLoading,
}) => {
  // Separate plans by interval
  const monthlyPlans = plans.filter(plan => plan.interval === 'month');
  const yearlyPlans = plans.filter(plan => plan.interval === 'year');

  return (
    <div className="space-y-6">
      {/* Enhanced Tabs Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-cyan-800 dark:from-gray-100 dark:via-blue-300 dark:to-cyan-300 bg-clip-text text-transparent">
            Choose Your Plan
          </h3>
          <p className="text-gray-600 dark:text-gray-400 font-medium mt-1">
            Select the perfect plan for your business needs
          </p>
        </div>
        <div className="hidden md:block text-sm text-gray-500 dark:text-gray-400 font-medium">
          {plans.length} plans available
        </div>
      </div>

      <Tabs defaultValue="monthly" className="space-y-6">
        <div className="flex items-center justify-center">
          <TabsList className="grid w-fit grid-cols-2 bg-gradient-to-r from-gray-50 via-blue-50/50 to-cyan-50/50 dark:from-gray-800/50 dark:via-blue-950/30 dark:to-cyan-950/30 border border-gray-200/50 dark:border-gray-700/50 shadow-lg backdrop-blur-sm p-1">
            <TabsTrigger
              value="monthly"
              className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-all duration-300 px-6 py-3"
            >
              <Calendar className="w-4 h-4" />
              Monthly
              {monthlyPlans.length > 0 && (
                <span className="ml-1 text-xs opacity-75">({monthlyPlans.length})</span>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="yearly"
              className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-cyan-500 data-[state=active]:to-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-all duration-300 px-6 py-3"
            >
              <Clock className="w-4 h-4" />
              Yearly
              {yearlyPlans.length > 0 && (
                <>
                  <span className="ml-1 text-xs opacity-75">({yearlyPlans.length})</span>
                  <span className="ml-1 px-1.5 py-0.5 bg-green-500/20 text-green-700 dark:text-green-400 text-xs rounded-full font-semibold">
                    Save 20%
                  </span>
                </>
              )}
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="monthly" className="space-y-4">
          {monthlyPlans.length > 0 ? (
            <PlanGrid
              plans={monthlyPlans}
              currentPlan={currentPlan || null}
              formatPrice={formatPrice}
              handlePlanUpgrade={handlePlanUpgrade}
              isLoading={isLoading}
              planType="monthly"
            />
          ) : (
            <div className="text-center py-12">
              <div className="flex flex-col items-center gap-4">
                <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-700">
                  <Calendar className="w-6 h-6 text-gray-400" />
                </div>
                <p className="text-gray-600 dark:text-gray-400 font-medium">
                  No monthly plans available
                </p>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="yearly" className="space-y-4">
          {yearlyPlans.length > 0 ? (
            <PlanGrid
              plans={yearlyPlans}
              currentPlan={currentPlan || null}
              formatPrice={formatPrice}
              handlePlanUpgrade={handlePlanUpgrade}
              isLoading={isLoading}
              planType="yearly"
            />
          ) : (
            <div className="text-center py-12">
              <div className="flex flex-col items-center gap-4">
                <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-700">
                  <Clock className="w-6 h-6 text-gray-400" />
                </div>
                <p className="text-gray-600 dark:text-gray-400 font-medium">
                  No yearly plans available
                </p>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PlanTabs;

import React from "react";
import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";
import { getReportById, getScheduledReportById } from "@/lib/actions/reports";
import { ScheduleReportForm } from "@/components/dashboard/reports/schedule-report-form";
import DashboardLayout from "@/components/layout/DashboardLayout";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface PageParams {
  id: string;
}

export default async function EditSchedulePage(
  props: {
    params: Promise<Promise<PageParams> | PageParams>;
  }
) {
  const params = await props.params;
  const { id } = params;
  const { userId } = await auth();

  if (!userId) {
    redirect("/sign-in");
  }

  try {
    // Get the scheduled report
    const scheduledReport = await getScheduledReportById(id);

    if (!scheduledReport) {
      redirect("/dashboard/reports");
    }

    // Get the report associated with the scheduled report
    const report = await getReportById(scheduledReport.reportId);

    if (!report) {
      redirect("/dashboard/reports");
    }

    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="flex items-center gap-4 mb-6">
            <Button asChild variant="subtle" size="icon">
              <Link href="/dashboard/reports">
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-semibold">Edit Schedule</h1>
              <p className="text-muted-foreground">Update the delivery schedule for your report</p>
            </div>
          </div>
          <div className="grid gap-8">
            <ScheduleReportForm
              report={report}
              scheduledReport={scheduledReport}
              isEditing={true}
            />
          </div>
        </div>
      </DashboardLayout>
    );
  } catch {
    redirect("/dashboard/reports");
  }
}

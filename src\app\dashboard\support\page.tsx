import DashboardLayout from '@/components/layout/DashboardLayout';
import { Suspense } from 'react';
import { Clock } from 'lucide-react';
import SupportForm from '@/components/dashboard/support/SupportForm';
import QuickHelpSection from '@/components/dashboard/support/QuickHelpSection';
import SupportSidebar from '@/components/dashboard/support/SupportSidebar';

// Loading skeleton component for SSR
const SupportLoadingSkeleton = () => (
  <div className="space-y-6">
    <div className="bg-gradient-to-r from-gray-50 via-blue-50/50 to-purple-50/50 dark:from-gray-800/50 dark:via-blue-950/30 dark:to-purple-950/30 border border-gray-200/50 dark:border-gray-700/50 rounded-3xl p-6 animate-pulse">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-xl"></div>
        <div className="space-y-2">
          <div className="w-32 h-6 bg-gray-300 dark:bg-gray-600 rounded"></div>
          <div className="w-48 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
      <div className="space-y-4">
        <div className="w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
        <div className="w-3/4 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="w-1/2 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    </div>
  </div>
);

export default function DashboardSupport() {
  return (
    <DashboardLayout>
      <div className="min-h-screen flex-col w-full">
        <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
          {/* Enhanced Header */}
          <div className="flex items-center justify-between space-y-2">
            <div>
              <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-gray-900 via-teal-800 to-emerald-800 dark:from-gray-100 dark:via-teal-300 dark:to-emerald-300 bg-clip-text text-transparent">
                Help & Support
              </h2>
              <p className="text-gray-600 dark:text-gray-400 font-medium mt-1">
                Get the help you need, when you need it
              </p>
            </div>
            <div className="hidden md:flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
              <Clock className="w-4 h-4" />
              <span>Response time: 2-4 hours</span>
            </div>
          </div>

          {/* Quick Help Section */}
          <Suspense fallback={<SupportLoadingSkeleton />}>
            <QuickHelpSection className="mb-8" />
          </Suspense>

          {/* Main Contact Form and Sidebar */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Suspense fallback={<SupportLoadingSkeleton />}>
                <SupportForm />
              </Suspense>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <SupportSidebar />
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

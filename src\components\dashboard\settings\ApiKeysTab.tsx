'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Pencil, Trash2, Check, X, Plus, Copy, Loader2 } from 'lucide-react';
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { DatePicker } from '@/components/ui/date-picker';

type ApiKey = {
  users_api_key_id: string;
  name: string;
  api_key: string;
  created_at?: string;
  last_used_at?: string;
  expires_at?: string;
  usage_24h?: number;
  chat_usage?: number;
  invoice_usage?: number;
  is_active?: boolean;
  active?: boolean;
};

type DbUser = {
  id: string;
  [key: string]: unknown;
};

type ApiKeyResponse = {
  users_api_key_id: string;
  name: string;
  api_key: string;
  created_at?: string;
  last_used_at?: string;
  expires_at?: string;
  is_active?: boolean;
  api_usages?: Array<{
    chatUsage?: number;
    invoiceUsage?: number;
  }>;
};

const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
if (!backendUrl) {
  throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not configured');
}

const ApiKeysTab: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [apiKeysLoading, setApiKeysLoading] = useState(false);
  const [apiKeysError, setApiKeysError] = useState<string | null>(null);
  const [apiKeyName, setApiKeyName] = useState("");
  const [search, setSearch] = useState("");
  const [editKeyId, setEditKeyId] = useState<string | null>(null);
  const [editKeyName, setEditKeyName] = useState("");
  const [toggleLoading, setToggleLoading] = useState<string | null>(null);
  const [dbUser, setDbUser] = useState<DbUser | null>(null);
  const [userLoading, setUserLoading] = useState(true);
  const [openApiDialog, setOpenApiDialog] = useState(false);
  const [expiresDate, setExpiresDate] = useState<Date | undefined>(() => {
    const d = new Date();
    d.setDate(d.getDate() + 30);
    return d;
  });

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('API key copied to clipboard.');
    } catch {
      toast.error('Failed to copy API key.');
    }
  };

  const handleViewApiKeys = useCallback(async () => {
    if (!dbUser?.id) return;
    setApiKeysLoading(true);
    setApiKeysError(null);
    setApiKeys([]);
    try {
      const res = await fetch(`${backendUrl}/api/v1/api-keys/user/${dbUser.id}`);
      if (!res.ok) throw new Error('Failed to fetch API keys');
      const data = await res.json();
      const mapped = Array.isArray(data)
        ? (data as ApiKeyResponse[]).map((k) => {
            const usage = Array.isArray(k.api_usages) && k.api_usages.length > 0 ? k.api_usages[0] : {};
            return {
              ...k,
              chat_usage: usage.chatUsage ?? 0,
              invoice_usage: usage.invoiceUsage ?? 0,
              active: !!k.is_active,
            };
          })
        : [];
      setApiKeys(mapped);
    } catch (err: unknown) {
      setApiKeysError(err instanceof Error ? err.message : 'Error fetching API keys');
    } finally {
      setApiKeysLoading(false);
    }
  }, [dbUser?.id]);

  useEffect(() => {
    async function fetchDbUser() {
      setUserLoading(true);
      try {
        const res = await fetch('/api/user/profile');
        if (!res.ok) throw new Error('Failed to fetch user');
        const data = await res.json();
        setDbUser(data);
      } catch {
        setDbUser(null);
      } finally {
        setUserLoading(false);
      }
    }
    fetchDbUser();
  }, []);

  useEffect(() => {
    if (dbUser) handleViewApiKeys();
  }, [dbUser, handleViewApiKeys]);

  const handleDeleteApiKey = async (key: ApiKey) => {
    if (!key?.api_key) return;
    try {
      const res = await fetch(`${backendUrl}/api/v1/api-keys/${key.api_key}`, {
        method: 'DELETE',
      });
      if (!res.ok) throw new Error('Failed to delete API key');
      toast.success(`${key.name || 'API Key'} deleted successfully.`);
      handleViewApiKeys();
    } catch (err: unknown) {
      toast.error(err instanceof Error ? err.message : 'Failed to delete API key.');
    }
  };

  const handleToggleActive = async (key: ApiKey) => {
    setToggleLoading(key.users_api_key_id);
    setApiKeys(prev => prev.map(k => k.users_api_key_id === key.users_api_key_id ? { ...k, active: !k.active } : k));
    try {
      const url = `${backendUrl}/api/v1/api-keys/${key.api_key}/toggle`;
      const res = await fetch(url, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
      });
      if (!res.ok) {
        throw new Error('Failed to toggle API key');
      }
    } catch (err: unknown) {
      setApiKeys(prev => prev.map(k => k.users_api_key_id === key.users_api_key_id ? { ...k, active: key.active } : k));
      toast.error(err instanceof Error ? err.message : 'Failed to toggle API key.');
    } finally {
      setToggleLoading(null);
    }
  };

  const handleEditKeyName = async (key: ApiKey) => {
    try {
      const res = await fetch(`${backendUrl}/api/v1/api-keys/${key.api_key}/name`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: editKeyName }),
      });
      if (!res.ok) throw new Error('Failed to update API key name');
      setApiKeys(prev => prev.map(k => k.users_api_key_id === key.users_api_key_id ? { ...k, name: editKeyName } : k));
      setEditKeyId(null);
      setEditKeyName("");
      toast.success('API key name updated successfully.');
    } catch (err: unknown) {
      toast.error(err instanceof Error ? err.message : 'Failed to update API key name.');
    }
  };

  const handleApiKeySave = async () => {
    if (!dbUser?.id) {
      toast.error('Cannot create API key without user ID.');
      return;
    }
    if (!apiKeyName.trim()) {
      toast.error('Please enter a name for your API key.');
      return;
    }
    setIsLoading(true);
    const expiresAt = expiresDate ? expiresDate.toISOString() : undefined;
    const payload = { user_id: dbUser.id, expires_at: expiresAt, name: apiKeyName };
    try {
      const response = await fetch(`${backendUrl}/api/v1/api-keys`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      if (!response.ok) throw new Error('Failed to create API key');
      toast.success('Your new API key has been generated.');
      setExpiresDate(() => {
        const d = new Date();
        d.setDate(d.getDate() + 30);
        return d;
      });
      setApiKeyName("");
      handleViewApiKeys();
      setOpenApiDialog(false);
    } catch {
      toast.error('Failed to create API key.');
    } finally {
      setIsLoading(false);
    }
  };

  if (userLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-4">
      <Card className="border-teal-200/30 dark:border-teal-700/20">
        <CardHeader className="border-b border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-r from-teal-50/50 via-white to-blue-50/50 dark:from-teal-950/20 dark:via-gray-800/50 dark:to-blue-950/20 rounded-t-3xl p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-teal-100 to-blue-100 dark:from-teal-900/40 dark:to-blue-900/40 shadow-sm">
              <svg className="w-5 h-5 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
              </svg>
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">API Keys</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                Manage your API keys and access tokens
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6 bg-gradient-to-br from-white via-teal-50/20 to-blue-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-blue-950/10 space-y-6">
          <div className="flex gap-3 mb-6">
            <Input
              placeholder="Search your API Keys..."
              value={search}
              onChange={e => setSearch(e.target.value)}
              className="flex-1 border-teal-200/50 dark:border-teal-700/50 focus:border-teal-500 dark:focus:border-teal-400 focus:ring-teal-500/20"
            />
            <Dialog open={openApiDialog} onOpenChange={setOpenApiDialog}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setOpenApiDialog(true)}
                  className="bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white border-none shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create API Key
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px] bg-gradient-to-br from-white via-teal-50/30 to-blue-50/30 dark:from-gray-900/95 dark:via-teal-950/30 dark:to-blue-950/30 border-teal-200/50 dark:border-teal-700/50">
                <DialogHeader>
                  <DialogTitle className="text-xl font-bold bg-gradient-to-r from-teal-600 to-blue-600 dark:from-teal-400 dark:to-blue-400 bg-clip-text text-transparent">
                    Create New API Key
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="api-key-name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      API Key Name
                    </Label>
                    <Input
                      id="api-key-name"
                      placeholder="Enter a descriptive name for your API key"
                      value={apiKeyName}
                      onChange={e => setApiKeyName(e.target.value)}
                      className="border-teal-200/50 dark:border-teal-700/50 focus:border-teal-500 dark:focus:border-teal-400 focus:ring-teal-500/20"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Expiration Date
                    </Label>
                    <DatePicker
                      date={expiresDate}
                      onDateChange={setExpiresDate}
                      placeholder="Select expiration date (optional)"
                      disablePastDates={true}
                      className="w-full border-teal-200/50 dark:border-teal-700/50 focus:border-teal-500 dark:focus:border-teal-400"
                    />
                  </div>
                </div>
                <DialogFooter className="flex flex-row justify-end gap-3">
                  <DialogClose asChild>
                    <Button
                      variant="outline"
                      onClick={() => setOpenApiDialog(false)}
                      className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800"
                    >
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button
                    onClick={handleApiKeySave}
                    disabled={isLoading || !apiKeyName.trim()}
                    className="bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Plus className="w-4 h-4 mr-2" />
                        Create API Key
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Enhanced Loading State */}
          {apiKeysLoading && (
            <div className="flex items-center justify-center py-12">
              <div className="flex flex-col items-center gap-3">
                <Loader2 className="w-8 h-8 animate-spin text-teal-500" />
                <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">Loading your API keys...</p>
              </div>
            </div>
          )}

          {/* Enhanced Error State */}
          {apiKeysError && (
            <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/50 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-red-700 dark:text-red-400 font-medium">{apiKeysError}</p>
              </div>
            </div>
          )}
          {!apiKeysLoading && !apiKeysError && (
            <div className="bg-gradient-to-br from-white/50 via-teal-50/30 to-blue-50/30 dark:from-gray-800/30 dark:via-teal-950/20 dark:to-blue-950/20 rounded-xl border border-teal-200/30 dark:border-teal-700/20 p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div className="p-1.5 rounded-lg bg-gradient-to-br from-teal-100 to-blue-100 dark:from-teal-900/40 dark:to-blue-900/40">
                    <svg className="w-4 h-4 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Your API Keys</h3>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400 font-medium">
                  {apiKeys.length} {apiKeys.length === 1 ? 'key' : 'keys'} total
                </div>
              </div>
              <div className="w-full overflow-x-auto">
                <div className="min-w-full bg-white/70 dark:bg-gray-800/70 rounded-lg border border-teal-200/50 dark:border-teal-700/50 overflow-hidden">
                  <table className="min-w-full text-sm">
                    <thead className="bg-gradient-to-r from-teal-50 to-blue-50 dark:from-teal-950/50 dark:to-blue-950/50">
                      <tr className="text-left">
                        <th className="px-4 py-3 font-semibold text-gray-700 dark:text-gray-300 text-xs uppercase tracking-wider">NAME</th>
                        <th className="px-4 py-3 font-semibold text-gray-700 dark:text-gray-300 text-xs uppercase tracking-wider">SECRET KEY</th>
                        <th className="px-4 py-3 font-semibold text-gray-700 dark:text-gray-300 text-xs uppercase tracking-wider">CREATED</th>
                        <th className="px-4 py-3 font-semibold text-gray-700 dark:text-gray-300 text-xs uppercase tracking-wider">LAST USED</th>
                        <th className="px-4 py-3 font-semibold text-gray-700 dark:text-gray-300 text-xs uppercase tracking-wider">USAGE (24HRS)</th>
                        <th className="px-4 py-3 font-semibold text-gray-700 dark:text-gray-300 text-xs uppercase tracking-wider">Chat Usage</th>
                        <th className="px-4 py-3 font-semibold text-gray-700 dark:text-gray-300 text-xs uppercase tracking-wider">Invoice Usage</th>
                        <th className="px-4 py-3 font-semibold text-gray-700 dark:text-gray-300 text-xs uppercase tracking-wider">ACTIONS</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200/50 dark:divide-gray-700/50">
                      {apiKeys.filter(key => key.name?.toLowerCase().includes(search.toLowerCase())).map((key: ApiKey, index) => (
                        <tr key={key.users_api_key_id} className={`${index % 2 === 0 ? 'bg-white/50 dark:bg-gray-800/30' : 'bg-gray-50/50 dark:bg-gray-700/30'}`}>
                          <td className="px-4 py-3 min-w-[140px]">
                            {editKeyId === key.users_api_key_id ? (
                              <div className="flex items-center gap-2">
                                <Input
                                  value={editKeyName}
                                  onChange={e => setEditKeyName(e.target.value)}
                                  className="h-8 text-sm px-3 border-teal-200/50 dark:border-teal-700/50 focus:border-teal-500 dark:focus:border-teal-400"
                                  autoFocus
                                />
                                <Button size="icon" variant="ghost" className="h-8 w-8 hover:bg-green-100 dark:hover:bg-green-900/30" onClick={() => handleEditKeyName(key)}>
                                  <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                                </Button>
                                <Button size="icon" variant="ghost" className="h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900/30" onClick={() => { setEditKeyId(null); setEditKeyName(""); }}>
                                  <X className="w-4 h-4 text-red-500 dark:text-red-400" />
                                </Button>
                              </div>
                            ) : (
                              <span className="font-semibold text-gray-900 dark:text-gray-100 text-sm">{key.name || 'Unnamed'}</span>
                            )}
                          </td>
                          <td className="px-4 py-3 min-w-[120px]">
                            {key.api_key ? (
                              <button
                                onClick={() => copyToClipboard(key.api_key)}
                                className="font-mono bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 hover:from-teal-100 hover:to-blue-100 dark:hover:from-teal-900/50 dark:hover:to-blue-900/50 px-3 py-2 rounded-lg cursor-pointer flex items-center gap-2 transition-all duration-200 text-sm border border-gray-300/50 dark:border-gray-600/50 hover:border-teal-300 dark:hover:border-teal-600 shadow-sm hover:shadow-md"
                                title="Click to copy API key"
                              >
                                <span className="text-gray-700 dark:text-gray-300">{key.api_key.slice(0, 8)}...{key.api_key.slice(-4)}</span>
                                <Copy className="w-3 h-3 text-gray-500 dark:text-gray-400" />
                              </button>
                            ) : (
                              <span className="font-mono text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg text-sm">Hidden</span>
                            )}
                          </td>
                          <td className="px-4 py-3 min-w-[110px]">
                            <span className="text-gray-600 dark:text-gray-400 text-sm">
                              {key.created_at ? new Date(key.created_at).toLocaleDateString() : '-'}
                            </span>
                          </td>
                          <td className="px-4 py-3 min-w-[110px]">
                            <span className="text-gray-600 dark:text-gray-400 text-sm">
                              {key.expires_at ? new Date(key.expires_at).toLocaleDateString() : '-'}
                            </span>
                          </td>
                          <td className="px-4 py-3 min-w-[110px]">
                            <div className="flex items-center gap-1">
                              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {(() => {
                                  const chatUsage = key.chat_usage === 0 ? 0 : (key.chat_usage ?? '-');
                                  const invoiceUsage = key.invoice_usage === 0 ? 0 : (key.invoice_usage ?? '-');

                                  if (chatUsage === '-' || invoiceUsage === '-') {
                                    return '-';
                                  }

                                  const totalUsage = chatUsage + invoiceUsage;
                                  return totalUsage;
                                })()}
                              </span>
                              {key.chat_usage > 0 || key.invoice_usage > 0 ? (
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              ) : (
                                <div className="w-2 h-2 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-3 min-w-[110px]">
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {key.chat_usage === 0 ? 0 : (key.chat_usage ?? '-')}
                            </span>
                          </td>
                          <td className="px-4 py-3 min-w-[110px]">
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {key.invoice_usage === 0 ? 0 : (key.invoice_usage ?? '-')}
                            </span>
                          </td>
                          <td className="px-4 py-3 min-w-[120px]">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center gap-2">
                                <Switch
                                  checked={!!key.active}
                                  onCheckedChange={() => handleToggleActive(key)}
                                  disabled={toggleLoading === key.users_api_key_id}
                                  className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-teal-500 data-[state=checked]:to-blue-600 data-[state=unchecked]:bg-gray-200 dark:data-[state=unchecked]:bg-gray-700 border-0 shadow-sm"
                                />
                                {toggleLoading === key.users_api_key_id && (
                                  <Loader2 className="w-3 h-3 animate-spin text-teal-500" />
                                )}
                              </div>
                              <div className="flex items-center gap-1">
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  className="h-8 w-8 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                                  onClick={() => { setEditKeyId(key.users_api_key_id); setEditKeyName(key.name || ''); }}
                                  title="Edit key name"
                                >
                                  <Pencil className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                                </Button>
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  className="h-8 w-8 hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors"
                                  onClick={() => handleDeleteApiKey(key)}
                                  title="Delete key"
                                >
                                  <Trash2 className="w-4 h-4 text-red-500 dark:text-red-400" />
                                </Button>
                              </div>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiKeysTab; 
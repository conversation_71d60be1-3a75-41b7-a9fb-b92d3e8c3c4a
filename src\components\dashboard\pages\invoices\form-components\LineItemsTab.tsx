"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Plus, X } from "lucide-react";

interface LineItem {
  id?: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate: number | null;
  taxAmount: number | null;
  discount: number | null;
  productSku: string | null;
  notes: string | null;
  attributes: Record<string, unknown>;
}

interface LineItemsTabProps {
  lineItems: LineItem[];
  handleLineItemChange: (index: number, field: string, value: unknown) => void;
  addLineItem: () => void;
  removeLineItem: (index: number) => void;
}

export function LineItemsTab({
  lineItems,
  handleLineItemChange,
  addLineItem,
  removeLineItem,
}: LineItemsTabProps) {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Line Items</h3>
        <Button type="button" variant="outline" size="sm" onClick={addLineItem}>
          <Plus className="h-4 w-4 mr-2" />
          Add Item
        </Button>
      </div>

      <ScrollArea className="h-[400px] pr-4">
        <div className="space-y-4">
          {lineItems.map((item, index) => (
            <Card key={index} className="relative">
              <Button
                type="button"
                variant="subtle"
                size="icon"
                className="absolute top-2 right-2 h-6 w-6"
                onClick={() => removeLineItem(index)}
              >
                <X className="h-4 w-4" />
              </Button>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Item {index + 1}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor={`item-${index}-description`}>Description</Label>
                  <Input
                    id={`item-${index}-description`}
                    value={item.description}
                    onChange={(e) => handleLineItemChange(index, "description", e.target.value)}
                    placeholder="Item description"
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`item-${index}-quantity`}>Quantity</Label>
                    <Input
                      id={`item-${index}-quantity`}
                      type="number"
                      step="0.01"
                      value={item.quantity}
                      onChange={(e) => handleLineItemChange(index, "quantity", e.target.value)}
                      placeholder="0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`item-${index}-unitPrice`}>Unit Price</Label>
                    <Input
                      id={`item-${index}-unitPrice`}
                      type="number"
                      step="0.01"
                      value={item.unitPrice}
                      onChange={(e) => handleLineItemChange(index, "unitPrice", e.target.value)}
                      placeholder="0.00"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`item-${index}-totalPrice`}>Total Price</Label>
                    <Input
                      id={`item-${index}-totalPrice`}
                      type="number"
                      step="0.01"
                      value={item.totalPrice}
                      onChange={(e) => handleLineItemChange(index, "totalPrice", e.target.value)}
                      placeholder="0.00"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`item-${index}-taxRate`}>Tax Rate (%)</Label>
                    <Input
                      id={`item-${index}-taxRate`}
                      type="number"
                      step="0.01"
                      value={item.taxRate || ""}
                      onChange={(e) => handleLineItemChange(index, "taxRate", e.target.value ? parseFloat(e.target.value) : null)}
                      placeholder="0.00"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`item-${index}-taxAmount`}>Tax Amount</Label>
                    <Input
                      id={`item-${index}-taxAmount`}
                      type="number"
                      step="0.01"
                      value={item.taxAmount || ""}
                      onChange={(e) => handleLineItemChange(index, "taxAmount", e.target.value ? parseFloat(e.target.value) : null)}
                      placeholder="0.00"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`item-${index}-discount`}>Discount</Label>
                    <Input
                      id={`item-${index}-discount`}
                      type="number"
                      step="0.01"
                      value={item.discount || ""}
                      onChange={(e) => handleLineItemChange(index, "discount", e.target.value ? parseFloat(e.target.value) : null)}
                      placeholder="0.00"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`item-${index}-productSku`}>Product SKU</Label>
                    <Input
                      id={`item-${index}-productSku`}
                      value={item.productSku || ""}
                      onChange={(e) => handleLineItemChange(index, "productSku", e.target.value)}
                      placeholder="SKU123"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`item-${index}-notes`}>Item Notes</Label>
                    <Input
                      id={`item-${index}-notes`}
                      value={item.notes || ""}
                      onChange={(e) => handleLineItemChange(index, "notes", e.target.value)}
                      placeholder="Additional notes"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}

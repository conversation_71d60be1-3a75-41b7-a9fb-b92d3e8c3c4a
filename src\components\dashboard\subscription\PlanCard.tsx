'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Check,
  CreditCard,
  Star,
  Shield,
} from 'lucide-react';

interface Plan {
  id: number;
  name: string;
  description: string | null;
  price: string;
  interval: string | null;
  productName: string | null;
  paddlePriceId: string | null;
}

interface PlanCardProps {
  plan: Plan;
  index: number;
  isCurrentPlan: boolean;
  formatPrice: (price: string) => string;
  handlePlanUpgrade: (priceId: string) => void;
  isLoading: boolean;
}

const PlanCard: React.FC<PlanCardProps> = ({
  plan,
  index,
  isCurrentPlan,
  formatPrice,
  handlePlanUpgrade,
  isLoading,
}) => {
  // Use static classes based on index
  let cardClasses = '';
  let badgeClasses = '';
  let headerClasses = '';
  let contentClasses = '';
  let iconClasses = '';
  let buttonClasses = '';
  let checkClasses = '';
  
  if (index % 3 === 0) {
    // Blue/Cyan theme
    cardClasses = 'relative border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10';
    badgeClasses = 'bg-gradient-to-r from-blue-500 to-cyan-600 text-white text-xs font-semibold py-1 px-3 rounded-full shadow-lg';
    headerClasses = 'border-b border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-r from-blue-50/50 via-white to-cyan-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20 rounded-t-3xl';
    contentClasses = 'p-6 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10 space-y-6';
    iconClasses = 'p-2 rounded-xl bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 shadow-sm';
    buttonClasses = 'w-full bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30 border-blue-200/50 dark:border-blue-700/50 hover:from-blue-100 hover:to-cyan-100 dark:hover:from-blue-950/50 dark:hover:to-cyan-950/50 text-blue-700 dark:text-blue-300 shadow-lg hover:shadow-xl transition-all duration-300';
    checkClasses = 'h-4 w-4 text-blue-600 dark:text-blue-400 mr-3';
    if (isCurrentPlan) cardClasses += ' ring-2 ring-blue-500/50';
  } else if (index % 3 === 1) {
    // Purple/Indigo theme
    cardClasses = 'relative border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10';
    badgeClasses = 'bg-gradient-to-r from-purple-500 to-indigo-600 text-white text-xs font-semibold py-1 px-3 rounded-full shadow-lg';
    headerClasses = 'border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/50 via-white to-indigo-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 rounded-t-3xl';
    contentClasses = 'p-6 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10 space-y-6';
    iconClasses = 'p-2 rounded-xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40 shadow-sm';
    buttonClasses = 'w-full bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950/30 dark:to-indigo-950/30 border-purple-200/50 dark:border-purple-700/50 hover:from-purple-100 hover:to-indigo-100 dark:hover:from-purple-950/50 dark:hover:to-indigo-950/50 text-purple-700 dark:text-purple-300 shadow-lg hover:shadow-xl transition-all duration-300';
    checkClasses = 'h-4 w-4 text-purple-600 dark:text-purple-400 mr-3';
    if (isCurrentPlan) cardClasses += ' ring-2 ring-purple-500/50';
  } else {
    // Teal/Emerald theme
    cardClasses = 'relative border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-br from-white via-teal-50/20 to-emerald-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-emerald-950/10';
    badgeClasses = 'bg-gradient-to-r from-teal-500 to-emerald-600 text-white text-xs font-semibold py-1 px-3 rounded-full shadow-lg';
    headerClasses = 'border-b border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-r from-teal-50/50 via-white to-emerald-50/50 dark:from-teal-950/20 dark:via-gray-800/50 dark:to-emerald-950/20 rounded-t-3xl';
    contentClasses = 'p-6 bg-gradient-to-br from-white via-teal-50/20 to-emerald-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-emerald-950/10 space-y-6';
    iconClasses = 'p-2 rounded-xl bg-gradient-to-br from-teal-100 to-emerald-100 dark:from-teal-900/40 dark:to-emerald-900/40 shadow-sm';
    buttonClasses = 'w-full bg-gradient-to-r from-teal-50 to-emerald-50 dark:from-teal-950/30 dark:to-emerald-950/30 border-teal-200/50 dark:border-teal-700/50 hover:from-teal-100 hover:to-emerald-100 dark:hover:from-teal-950/50 dark:hover:to-emerald-950/50 text-teal-700 dark:text-teal-300 shadow-lg hover:shadow-xl transition-all duration-300';
    checkClasses = 'h-4 w-4 text-teal-600 dark:text-teal-400 mr-3';
    if (isCurrentPlan) cardClasses += ' ring-2 ring-teal-500/50';
  }

  return (
    <Card className={cardClasses}>
      {isCurrentPlan && (
        <div className="absolute -top-2 -right-2">
          <div className={badgeClasses}>
            Current Plan
          </div>
        </div>
      )}
      <CardHeader className={headerClasses}>
        <div className="flex items-center gap-3">
          <div className={iconClasses}>
            {plan.productName?.includes('Pro') && (
              <Star className="w-5 h-5 text-amber-600 dark:text-amber-400" />
            )}
            {plan.productName?.includes('Enterprise') && (
              <Shield className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            )}
            {!plan.productName?.includes('Pro') && !plan.productName?.includes('Enterprise') && (
              <CreditCard className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            )}
          </div>
          <div>
            <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">
              {plan.productName || plan.name}
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
              {plan.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className={contentClasses}>
        <div className="space-y-4">
          <div className="text-center">
            <div className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              {formatPrice(plan.price)}
              <span className="text-lg font-normal text-gray-600 dark:text-gray-400">
                /{plan.interval || 'month'}
              </span>
            </div>
          </div>
          {isCurrentPlan ? (
            <Button className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg" disabled>
              <Check className="mr-2 h-4 w-4" />
              Current Plan
            </Button>
          ) : (
            <Button
              variant="outline"
              className={buttonClasses}
              onClick={() =>
                plan.paddlePriceId &&
                handlePlanUpgrade(plan.paddlePriceId)
              }
              disabled={isLoading}
            >
              {isLoading
                ? 'Processing...'
                : plan.productName?.includes(
                  'Enterprise'
                )
                  ? 'Contact Sales'
                  : `Start with ${plan.productName || plan.name}`}
            </Button>
          )}
        </div>
        <div className="border-t border-gray-200/30 dark:border-gray-700/20 pt-4">
          <div className="space-y-3 text-sm">
            {/* Plan features based on productName */}
            <div className="flex items-center">
              <Check className={checkClasses} />
              <span className="text-gray-600 dark:text-gray-400 font-medium">
                {plan.productName?.includes('Starter')
                  ? '10'
                  : plan.productName?.includes('Business')
                    ? '100'
                    : plan.productName?.includes(
                      'Enterprise'
                    )
                      ? '1000'
                      : '50'}{' '}
                invoice uploads/{plan.interval || 'month'}
              </span>
            </div>
            <div className="flex items-center">
              <Check className={checkClasses} />
              <span className="text-gray-600 dark:text-gray-400 font-medium">
                {plan.productName?.includes('Starter')
                  ? '5'
                  : plan.productName?.includes('Business')
                    ? '50'
                    : plan.productName?.includes(
                      'Enterprise'
                    )
                      ? '500'
                      : '25'}{' '}
                chat messages/{plan.interval || 'month'}
              </span>
            </div>
            <div className="flex items-center">
              <Check className={checkClasses} />
              <span className="text-gray-600 dark:text-gray-400 font-medium">
                {plan.productName?.includes('Basic')
                  ? 'Basic'
                  : plan.productName?.includes('Pro')
                    ? 'Advanced'
                    : plan.productName?.includes(
                      'Starter'
                    )
                      ? 'Limited'
                      : 'Premium'}{' '}
                OCR extraction
              </span>
            </div>
            <div className="flex items-center">
              <Check className={checkClasses} />
              <span className="text-gray-600 dark:text-gray-400 font-medium">
                {plan.productName?.includes('Basic')
                  ? 'Basic'
                  : plan.productName?.includes('Pro')
                    ? 'Advanced'
                    : plan.productName?.includes(
                      'Starter'
                    )
                      ? 'Basic'
                      : 'Premium'}{' '}
                analytics dashboard
              </span>
            </div>
            <div className="flex items-center">
              <Check className={checkClasses} />
              <span className="text-gray-600 dark:text-gray-400 font-medium">
                {plan.productName?.includes('Enterprise')
                  ? 'Priority'
                  : plan.productName?.includes('Pro')
                    ? 'Priority'
                    : 'Email'}{' '}
                support
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PlanCard;

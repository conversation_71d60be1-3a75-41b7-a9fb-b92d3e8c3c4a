import React, { useState, useEffect } from 'react';
import { UserRole } from '@prisma/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, AlertCircle, Building, Mail, Shield, Users } from 'lucide-react';
import { inviteTeamMember } from '@/lib/actions/team';
import { toast } from 'sonner';
import { z } from 'zod';
import { useOrganization } from '@clerk/nextjs';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface InviteFormProps {
  onSuccess: () => void;
  useClerkInvitations?: boolean; // Optional - defaults to environment variable
}

interface LocalOrganization {
  id: string;
  name: string;
}

// Email validation schema
const emailSchema = z
  .string()
  .email('Please enter a valid email address');

export default function InviteForm({
  onSuccess,
  useClerkInvitations,
}: InviteFormProps) {
  // Use prop if provided, otherwise fall back to environment variable
  const shouldUseClerkInvitations =
    useClerkInvitations ??
    process.env.NEXT_PUBLIC_USE_CLERK_INVITATIONS === 'true';

  const [isOpen, setIsOpen] = useState(false);
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<UserRole>(UserRole.VIEWER);
  const [emailError, setEmailError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCreatingOrg, setIsCreatingOrg] = useState(false);
  const [organizationName, setOrganizationName] = useState('');
  const [localOrganization, setLocalOrganization] = useState<LocalOrganization | null>(null);

  // Clerk hooks for built-in invitations
  const { organization: clerkOrganization, isLoaded: orgLoaded } = useOrganization();

  // Check if we need to create an organization
  const needsOrganization = orgLoaded && !clerkOrganization && !localOrganization;

  // Load local organization on component mount
  useEffect(() => {
    const loadLocalOrganization = async () => {
      try {
        const response = await fetch('/api/organizations/current');
        if (response.ok) {
          const data = await response.json();
          if (data.organization) {
            setLocalOrganization(data.organization);
          }
        }
      } catch (error) {
        console.error('Error loading local organization:', error);
      }
    };

    loadLocalOrganization();
  }, []);

  // Reset form
  const resetForm = () => {
    setEmail('');
    setRole(UserRole.VIEWER);
    setEmailError('');
    setOrganizationName('');
  };

  // Validate email
  const validateEmail = (email: string) => {
    try {
      emailSchema.parse(email);
      setEmailError('');
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        setEmailError(error.errors[0].message);
      } else {
        setEmailError('Please enter a valid email address');
      }
      return false;
    }
  };

  // Create organization
  const handleCreateOrganization = async () => {
    if (!organizationName.trim()) {
      toast.error('Please enter an organization name');
      return;
    }

    setIsCreatingOrg(true);
    try {
      const response = await fetch('/api/organizations/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: organizationName.trim() }),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success('Organization created successfully!');
        setLocalOrganization(data.organization);
        setIsOpen(false);
        onSuccess();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create organization');
      }
    } catch (error) {
      console.error('Error creating organization:', error);
      toast.error('Failed to create organization');
    } finally {
      setIsCreatingOrg(false);
    }
  };

  // Handle invite submission with Clerk
  const handleClerkInvite = async () => {
    if (!validateEmail(email)) return;
    if (!clerkOrganization) {
      toast.error('No organization found');
      return;
    }

    setIsSubmitting(true);
    try {
      // Map UserRole to Clerk role format
      const clerkRole =
        role === UserRole.ADMIN
          ? 'org:admin'
          : role === UserRole.EDITOR
            ? 'org:editor'
            : 'org:member';

      const invitation = await clerkOrganization.inviteMember({
        emailAddress: email,
        role: clerkRole,
      });

      if (invitation) {
        toast.success(`Invitation sent to ${email}`);
        setIsOpen(false);
        onSuccess();
        resetForm();
      }
    } catch (error) {
      console.error('Clerk invitation error:', error);
      toast.error('Failed to send invitation via Clerk');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle invite submission with custom system
  const handleCustomInvite = async () => {
    if (!validateEmail(email)) return;

    setIsSubmitting(true);
    try {
      const result = await inviteTeamMember(email, role);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(`Invitation sent to ${email}`);
        setIsOpen(false);
        onSuccess();
        resetForm();
      }
    } catch {
      toast.error('Failed to send invitation');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Choose which invite handler to use
  const handleInvite = shouldUseClerkInvitations
    ? handleClerkInvite
    : handleCustomInvite;

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open);
        if (!open) resetForm();
      }}
    >
      <DialogTrigger asChild>
        <Button className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0">
          <Plus className="mr-2 h-4 w-4" />
          Invite Team Member {shouldUseClerkInvitations && '(Clerk)'}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] bg-gradient-to-br from-white via-purple-50/10 to-indigo-50/10 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10 border border-purple-200/30 dark:border-purple-700/20">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Invite Team Member</DialogTitle>
          <DialogDescription className="text-gray-600 dark:text-gray-400">
            Send an invitation to join your organization.
            {shouldUseClerkInvitations &&
              " (Using Clerk's built-in system)"}
          </DialogDescription>
        </DialogHeader>

        {/* Show organization setup if no organization exists */}
        {needsOrganization ? (
          <div className="space-y-4">
            <Alert className="border-purple-200/50 dark:border-purple-700/50 bg-purple-50/50 dark:bg-purple-950/30">
              <AlertCircle className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              <AlertDescription className="text-purple-700 dark:text-purple-300">
                To send invitations, you need to create an organization first.
              </AlertDescription>
            </Alert>

            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="orgName" className="text-gray-900 dark:text-gray-100 font-medium">Organization Name</Label>
                <Input
                  id="orgName"
                  placeholder="My Company"
                  value={organizationName}
                  onChange={(e) =>
                    setOrganizationName(e.target.value)
                  }
                  className="border-purple-200/50 dark:border-purple-700/50 focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500/20"
                />
              </div>
            </div>

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setIsOpen(false);
                  toast.info('Please create an organization first');
                }}
                className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateOrganization}
                disabled={isCreatingOrg || !organizationName.trim()}
                className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0"
              >
                <Building className="mr-2 h-4 w-4" />
                {isCreatingOrg
                  ? 'Creating...'
                  : 'Create Organization'}
              </Button>
            </DialogFooter>
          </div>
        ) : (
          /* Regular invitation form */
          <div className="space-y-4">
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="email" className="text-gray-900 dark:text-gray-100 font-medium">Email address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    if (emailError) validateEmail(e.target.value);
                  }}
                  className="border-purple-200/50 dark:border-purple-700/50 focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500/20"
                />
                {emailError && (
                  <p className="text-sm text-red-500">{emailError}</p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="role" className="text-gray-900 dark:text-gray-100 font-medium">Role</Label>
                <Select
                  value={role}
                  onValueChange={(value) =>
                    setRole(value as UserRole)
                  }
                >
                  <SelectTrigger id="role" className="border-purple-200/50 dark:border-purple-700/50 focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500/20">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={UserRole.ADMIN}>
                      Admin
                    </SelectItem>
                    <SelectItem value={UserRole.EDITOR}>
                      Editor
                    </SelectItem>
                    <SelectItem value={UserRole.VIEWER}>
                      Viewer
                    </SelectItem>
                  </SelectContent>
                </Select>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {role === UserRole.ADMIN && (
                    <p className="flex items-start">
                      <Shield className="mr-2 h-4 w-4 text-blue-500 mt-0.5" />
                      Admins can manage team members, subscription,
                      and have full access to all features.
                    </p>
                  )}
                  {role === UserRole.EDITOR && (
                    <p className="flex items-start">
                      <Users className="mr-2 h-4 w-4 text-green-500 mt-0.5" />
                      Editors can create and edit content but cannot
                      manage team members or subscription settings.
                    </p>
                  )}
                  {role === UserRole.VIEWER && (
                    <p className="flex items-start">
                      <Mail className="mr-2 h-4 w-4 text-amber-500 mt-0.5" />
                      Viewers have read-only access to content but
                      cannot make any changes.
                    </p>
                  )}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="submit"
                onClick={handleInvite}
                disabled={!email || isSubmitting}
                className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0"
              >
                {isSubmitting ? 'Sending...' : 'Send Invitation'}
              </Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from '@/components/ui/tabs';
import {
  CreditCard,
  FileText,
  AlertTriangle,
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { handleUpgrade } from '@/actions/subscription-actions';
import { toast } from 'sonner';
import { format } from 'date-fns';
import CurrentPlanCard from '@/components/dashboard/subscription/CurrentPlanCard';
import UsageCard from '@/components/dashboard/subscription/UsageCard';
import PlanTabs from '@/components/dashboard/subscription/PlanTabs';
import BillingHistoryCard from '@/components/dashboard/subscription/BillingHistoryCard';

interface SubscriptionProps {
  userSubscriptions: {
    id: number;
    paddleSubscriptionId?: string | null;
    provider?: string;
    orderId?: number | null;
    name: string;
    email: string;
    status: string;
    statusFormatted: string;
    renewsAt: string | null;
    endsAt: string | null;
    trialEndsAt: string | null;
    price: string;
    isPaused: boolean | null;
    subscriptionItemId: string | number;
    isUsageBased: boolean | null;
    userId: string;
    planId: number;
    cancelUrl?: string | null;
    updateUrl?: string | null;
  }[];
  plans: {
    id: number;
    name: string;
    description: string | null;
    price: string;
    interval: string | null;
    intervalCount: number | null;
    isUsageBased: boolean | null;
    productId: number;
    productName: string | null;
    variantId: number;
    paddlePriceId: string | null;
    trialInterval: string | null;
    trialIntervalCount: number | null;
    sort: number | null;
  }[];
  successParams?: {
    sessionId?: string;
    returnUrl?: string;
  };
}

interface UsageStats {
  chatUsage: number;
  invoiceUsage: number;
  chatLimit: number;
  invoiceLimit: number;
  resetDate: Date;
  daysUntilReset: number;
}

interface BillingTransaction {
  description?: string;
  date?: string;
  amount?: string;
  status?: string;
}

type UserSubscription = SubscriptionProps['userSubscriptions'][0];

// Type guard to check if a subscription exists and is active
function isActiveSubscription(
  subscription: UserSubscription | null | undefined
): subscription is UserSubscription {
  return (
    subscription !== null &&
    subscription !== undefined &&
    subscription.status === 'active'
  );
}

function formatPrice(price: string | number) {
  const cents = typeof price === 'string' ? parseInt(price) : price;
  return `$${(cents / 100).toFixed(2)}`;
}

const Subscription = ({
  userSubscriptions,
  plans,
  successParams,
}: SubscriptionProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [portalLoading, setPortalLoading] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);
  const [billingHistory, setBillingHistory] = useState<BillingTransaction[]>([]);
  const [billingLoading, setBillingLoading] = useState(false);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(
    null
  );
  const [usageLoading, setUsageLoading] = useState(true);
  const [limitExceededInfo, setLimitExceededInfo] = useState<{
    feature: string;
    message: string;
  } | null>(null);

  // Handle success redirect with client-side navigation
  useEffect(() => {
    if (successParams?.returnUrl) {
      // Show success message
      toast.success('Subscription activated successfully!');

      // Try to activate subscription manually in case webhook is delayed
      const activateSubscription = async () => {
        if (successParams.sessionId) {
          try {
            const response = await fetch('/api/subscriptions/activate', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                sessionId: successParams.sessionId,
              }),
            });

            const result = await response.json();
            console.log('Subscription activation result:', result);
          } catch (error) {
            console.error('Error activating subscription:', error);
          }
        }
      };

      // Activate subscription immediately
      activateSubscription();

      // Small delay to ensure webhook has processed, then redirect
      const timer = setTimeout(() => {
        const returnUrl = decodeURIComponent(successParams.returnUrl!);
        console.log('Redirecting to return URL:', returnUrl);
        
        // Force a hard navigation to ensure proper state refresh
        window.location.href = returnUrl;
      }, 3000); // Increased delay to allow webhook processing

      return () => clearTimeout(timer);
    }
  }, [successParams]);

  // Handle usage limit exceeded messages from URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const limitExceeded = urlParams.get('limit_exceeded');
    const message = urlParams.get('message');

    if (limitExceeded && message) {
      setLimitExceededInfo({
        feature: limitExceeded,
        message: decodeURIComponent(message),
      });

      toast.error(decodeURIComponent(message), {
        duration: 5000,
        action: {
          label: 'Upgrade Now',
          onClick: () => {
            // Scroll to plans section
            const plansTab =
              document.querySelector('[value="plans"]');
            if (plansTab) {
              (plansTab as HTMLElement).click();
            }
          },
        },
      });

      // Clean up URL parameters
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('limit_exceeded');
      newUrl.searchParams.delete('message');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, []);

  // Fetch billing history when component mounts
  useEffect(() => {
    const fetchBillingHistory = async () => {
      setBillingLoading(true);
      try {
        const response = await fetch('/api/billing/history');
        if (response.ok) {
          const result = await response.json();
          setBillingHistory(result.data || []);
        }
      } catch (error) {
        console.error('Error fetching billing history:', error);
      } finally {
        setBillingLoading(false);
      }
    };

    fetchBillingHistory();
  }, []);

  // Fetch usage stats when component mounts
  useEffect(() => {
    const fetchUsageStats = async () => {
      setUsageLoading(true);
      try {
        const response = await fetch('/api/usage/stats');
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setUsageStats({
              ...result.stats,
              resetDate: new Date(result.stats.resetDate),
            });
          }
        }
      } catch (error) {
        console.error('Error fetching usage stats:', error);
      } finally {
        setUsageLoading(false);
      }
    };

    fetchUsageStats();
  }, []);

  // Get the active subscription if any
  const activeSubscription =
    userSubscriptions.find((sub) => sub.status === 'active') || null;

  // Calculate usage percentages
  const documentsPercentage =
    usageStats && usageStats.invoiceLimit > 0
      ? (usageStats.invoiceUsage / usageStats.invoiceLimit) * 100
      : 0;

  const chatPercentage =
    usageStats && usageStats.chatLimit > 0
      ? (usageStats.chatUsage / usageStats.chatLimit) * 100
      : 0;

  // Get the current plan details
  const currentPlan =
    activeSubscription && activeSubscription.planId
      ? plans.find((plan) => plan.id === activeSubscription.planId)
      : null;

  // Format next billing date
  const nextBillingDate = activeSubscription?.renewsAt
    ? format(new Date(activeSubscription.renewsAt), 'MMMM dd, yyyy')
    : 'N/A';

  // Function to handle plan cancellation based on provider
  const handlePlanCancel = async () => {
    if (!isActiveSubscription(activeSubscription)) return;

    try {
      setCancelLoading(true);

      if (
        activeSubscription.provider === 'paddle' &&
        activeSubscription.paddleSubscriptionId
      ) {
        const response = await fetch(
          '/api/paddle/cancel-subscription',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              subscriptionId: activeSubscription.paddleSubscriptionId,
            }),
          }
        );

        const result = await response.json();

        if (result.success) {
          toast.success('Subscription canceled successfully');
          window.location.reload();
        } else {
          toast.error(
            result.error || 'Failed to cancel subscription'
          );
        }
      } else {
        toast.error(
          'Unable to identify subscription provider - only Paddle subscriptions are supported'
        );
      }
    } catch {
      toast.error('Failed to cancel subscription');
    } finally {
      setCancelLoading(false);
    }
  };

  // Function to handle customer portal access based on provider
  const handleCustomerPortal = async () => {
    if (!isActiveSubscription(activeSubscription)) return;

    try {
      setPortalLoading(true);

      if (
        activeSubscription.provider === 'paddle' &&
        activeSubscription.paddleSubscriptionId
      ) {
        const response = await fetch('/api/paddle/customer-portal', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            subscriptionId: activeSubscription.paddleSubscriptionId,
          }),
        });

        const result = await response.json();

        if (result.success && result.data?.update_payment_method) {
          window.open(result.data.update_payment_method, '_blank');
        } else {
          toast.error(
            result.error || 'Unable to access customer portal'
          );
        }
      } else {
        toast.error(
          'Unable to identify subscription provider - only Paddle subscriptions are supported'
        );
      }
    } catch {
      toast.error('Failed to access customer portal');
    } finally {
      setPortalLoading(false);
    }
  };

  // Function to handle plan upgrade
  const handlePlanUpgrade = async (
    priceId: string | undefined | null
  ) => {
    if (!priceId) {
      return;
    }
    try {
      setIsLoading(true);
      await handleUpgrade(priceId);
    } catch {
      setIsLoading(false);
    }
  };

  // Sort plans by price for display
  const sortedPlans = [...plans].sort((a, b) => {
    const priceA = parseInt(a.price) || 0;
    const priceB = parseInt(b.price) || 0;
    return priceA - priceB;
  });

  // Get provider display name
  const getProviderDisplay = (provider?: string) => {
    switch (provider) {
      case 'paddle':
        return 'Paddle';
      default:
        return 'Payment Provider';
    }
  };

  return (
    <div className="min-h-screen flex-col w-full">
      <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
        {/* Enhanced Header */}
        <div className="flex flex-col items-start justify-between space-y-2 md:flex-row md:items-center md:space-y-0">
          <div>
            <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-gray-900 via-blue-800 to-cyan-800 dark:from-gray-100 dark:via-blue-300 dark:to-cyan-300 bg-clip-text text-transparent">
              Subscription
            </h2>
            <p className="text-gray-600 dark:text-gray-400 font-medium mt-1">
              Manage your subscription, billing, and usage
            </p>
          </div>
        </div>

        {/* Enhanced Usage Limit Exceeded Alert */}
        {limitExceededInfo && (
          <Alert className="border-red-200/50 dark:border-red-800/50 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            <AlertDescription className="text-red-700 dark:text-red-400 font-medium">
              <strong>Usage Limit Reached:</strong>{' '}
              {limitExceededInfo.message}
            </AlertDescription>
          </Alert>
        )}

        {/* Enhanced Current Plan and Usage Cards */}
        <div className="grid gap-6 md:grid-cols-2">
          <CurrentPlanCard
            currentPlan={currentPlan}
            activeSubscription={activeSubscription}
            nextBillingDate={nextBillingDate}
            formatPrice={formatPrice}
            getProviderDisplay={getProviderDisplay}
            handleCustomerPortal={handleCustomerPortal}
            handlePlanCancel={handlePlanCancel}
            portalLoading={portalLoading}
            cancelLoading={cancelLoading}
          />
          <UsageCard
            currentPlan={currentPlan}
            usageStats={usageStats}
            usageLoading={usageLoading}
            documentsPercentage={documentsPercentage}
            chatPercentage={chatPercentage}
          />

        </div>

        {/* Enhanced Tabs Section */}
        <Tabs defaultValue="plans" className="space-y-6">
          <div className="flex items-center justify-between">
            <TabsList className="grid w-fit grid-cols-2 bg-gradient-to-r from-gray-50 via-blue-50/50 to-cyan-50/50 dark:from-gray-800/50 dark:via-blue-950/30 dark:to-cyan-950/30 border border-gray-200/50 dark:border-gray-700/50 shadow-lg backdrop-blur-sm">
              <TabsTrigger
                value="plans"
                className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-all duration-300"
              >
                <CreditCard className="w-4 h-4" />
                Available Plans
              </TabsTrigger>
              <TabsTrigger
                value="billing"
                className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-blue-600 data-[state=active]:text-white data-[state=active]:shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-all duration-300"
              >
                <FileText className="w-4 h-4" />
                Billing History
              </TabsTrigger>
            </TabsList>

            {/* Tab Info */}
            <div className="hidden md:block text-sm text-gray-500 dark:text-gray-400 font-medium">
              {sortedPlans.length} plans available
            </div>
          </div>

          <TabsContent value="plans" className="space-y-4">
            <PlanTabs
              plans={sortedPlans}
              currentPlan={currentPlan}
              formatPrice={formatPrice}
              handlePlanUpgrade={handlePlanUpgrade}
              isLoading={isLoading}
            />
          </TabsContent>

          <TabsContent value="billing" className="space-y-4">
            <BillingHistoryCard
              billingHistory={billingHistory}
              billingLoading={billingLoading}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Subscription;

'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  CreditCard,
  Calendar,
  DollarSign,
} from 'lucide-react';

interface CurrentPlanCardProps {
  currentPlan: any;
  activeSubscription: any;
  nextBillingDate: string;
  formatPrice: (price: string) => string;
  getProviderDisplay: (provider: string) => string;
  handleCustomerPortal: () => void;
  handlePlanCancel: () => void;
  portalLoading: boolean;
  cancelLoading: boolean;
}

const CurrentPlanCard: React.FC<CurrentPlanCardProps> = ({
  currentPlan,
  activeSubscription,
  nextBillingDate,
  formatPrice,
  getProviderDisplay,
  handleCustomerPortal,
  handlePlanCancel,
  portalLoading,
  cancelLoading,
}) => {
  return (
    <Card className="border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10">
      <CardHeader className="border-b border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-r from-blue-50/50 via-white to-cyan-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20 rounded-t-3xl">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-xl bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 shadow-sm">
            <CreditCard className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Current Plan</CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
              {currentPlan
                ? `You are currently on the ${currentPlan.name} plan`
                : "You don't have an active subscription"}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10 space-y-6">
        {currentPlan && activeSubscription ? (
          <>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Badge className="bg-gradient-to-r from-blue-500 to-cyan-600 text-white shadow-lg px-3 py-1">
                  {currentPlan.name}
                </Badge>
                <span className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                  {formatPrice(currentPlan.price)}/
                  {currentPlan.interval}
                </span>
              </div>
              <div className="flex flex-col items-end gap-2">
                <Badge
                  variant="outline"
                  className="bg-green-50 dark:bg-green-950/20 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800/50 px-3 py-1 shadow-sm"
                >
                  {activeSubscription.statusFormatted ||
                    'Active'}
                </Badge>
                <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                  via{' '}
                  {getProviderDisplay(
                    activeSubscription.provider
                  )}
                </span>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-blue-200/50 dark:border-blue-700/50">
                <span className="text-gray-600 dark:text-gray-400 flex items-center gap-2 font-medium">
                  <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  Next billing date
                </span>
                <span className="font-semibold text-gray-900 dark:text-gray-100">
                  {nextBillingDate}
                </span>
              </div>
              <div className="flex items-center justify-between p-3 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-blue-200/50 dark:border-blue-700/50">
                <span className="text-gray-600 dark:text-gray-400 flex items-center gap-2 font-medium">
                  <DollarSign className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  Amount
                </span>
                <span className="font-semibold text-gray-900 dark:text-gray-100">
                  {formatPrice(currentPlan.price)}/
                  {currentPlan.interval}
                </span>
              </div>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                className="flex-1 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30 border-blue-200/50 dark:border-blue-700/50 hover:from-blue-100 hover:to-cyan-100 dark:hover:from-blue-950/50 dark:hover:to-cyan-950/50 text-blue-700 dark:text-blue-300"
                onClick={handleCustomerPortal}
                disabled={portalLoading}
              >
                <CreditCard className="mr-2 h-4 w-4" />
                {portalLoading
                  ? 'Loading...'
                  : 'Manage Billing'}
              </Button>
              <Button
                variant="destructive"
                onClick={handlePlanCancel}
                disabled={cancelLoading}
                className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 shadow-lg hover:shadow-xl"
              >
                {cancelLoading ? 'Canceling...' : 'Cancel'}
              </Button>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <div className="flex flex-col items-center gap-4">
              <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-700">
                <CreditCard className="w-6 h-6 text-gray-400" />
              </div>
              <p className="text-gray-600 dark:text-gray-400 font-medium mb-2">
                You don&apos;t have an active subscription
              </p>
              <Button className="bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl">
                Choose a Plan
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CurrentPlanCard;

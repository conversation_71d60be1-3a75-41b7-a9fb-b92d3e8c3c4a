'use client';

import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { useState, useCallback, Suspense } from 'react';
import SecurityTab from '@/components/dashboard/settings/SecurityTab';
import AdvancedTab from '@/components/dashboard/settings/AdvancedTab';
import ApiKeysTab from '@/components/dashboard/settings/ApiKeysTab';
import { Shield, Settings as SettingsIcon, Key, Loader2 } from 'lucide-react';


// Loading component for tab content
const TabLoadingSkeleton = () => (
  <div className="space-y-4">
    <div className="bg-gradient-to-r from-gray-50 via-blue-50/50 to-purple-50/50 dark:from-gray-800/50 dark:via-blue-950/30 dark:to-purple-950/30 border border-gray-200/50 dark:border-gray-700/50 rounded-3xl p-6 animate-pulse">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-xl"></div>
        <div className="space-y-2">
          <div className="w-32 h-6 bg-gray-300 dark:bg-gray-600 rounded"></div>
          <div className="w-48 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
      <div className="space-y-4">
        <div className="w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
        <div className="w-3/4 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="w-1/2 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
    </div>
  </div>
);

const Settings = () => {
  const [activeTab, setActiveTab] = useState('account');
  const [isTabLoading, setIsTabLoading] = useState(false);
  const currentDate = useCallback(() => new Date().toLocaleDateString(), []);

  const handleTabChange = useCallback((newTab: string) => {
    if (newTab !== activeTab) {
      setIsTabLoading(true);
      // Simulate loading time for better UX
      setTimeout(() => {
        setActiveTab(newTab);
        setIsTabLoading(false);
      }, 300);
    }
  }, [activeTab]);

  return (
    <DashboardLayout>
      <div className="min-h-screen flex-col w-full">
        <div className="flex-1 space-y-6 p-4 pt-6 md:p-8">
          {/* Enhanced Header */}
          <div className="flex items-center justify-between space-y-2">
            <div>
              <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-gray-900 via-indigo-800 to-blue-800 dark:from-gray-100 dark:via-indigo-300 dark:to-blue-300 bg-clip-text text-transparent">
                Settings
              </h2>
              <p className="text-gray-600 dark:text-gray-400 font-medium mt-1">
                Manage your account, security, and API preferences
              </p>
            </div>
          </div>

          {/* Enhanced Tabs Section */}
          <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6" id="settings-tabs">
            <div className="flex items-center justify-between">
              <TabsList className="grid w-fit grid-cols-3 bg-gradient-to-r from-gray-50 via-indigo-50/50 to-blue-50/50 dark:from-gray-800/50 dark:via-indigo-950/30 dark:to-blue-950/30 border border-gray-200/50 dark:border-gray-700/50 shadow-lg backdrop-blur-sm">
                <TabsTrigger
                  value="account"
                  className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-all duration-300"
                >
                  <Shield className="w-4 h-4" />
                  Account
                </TabsTrigger>
                <TabsTrigger
                  value="advanced"
                  className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-all duration-300"
                >
                  <SettingsIcon className="w-4 h-4" />
                  Advanced
                </TabsTrigger>
                <TabsTrigger
                  value="api-keys"
                  className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-teal-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-lg hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-all duration-300"
                >
                  <Key className="w-4 h-4" />
                  API Keys
                </TabsTrigger>
              </TabsList>

              {/* Tab Info */}
              <div className="hidden md:block text-sm text-gray-500 dark:text-gray-400 font-medium">
                Last updated: {currentDate()}
              </div>
            </div>

            <TabsContent value="account" className="space-y-4">
              {isTabLoading ? (
                <TabLoadingSkeleton />
              ) : (
                <Suspense fallback={<TabLoadingSkeleton />}>
                  <SecurityTab />
                </Suspense>
              )}
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              {isTabLoading ? (
                <TabLoadingSkeleton />
              ) : (
                <Suspense fallback={<TabLoadingSkeleton />}>
                  <AdvancedTab />
                </Suspense>
              )}
            </TabsContent>

            <TabsContent value="api-keys" className="space-y-4">
              {isTabLoading ? (
                <TabLoadingSkeleton />
              ) : (
                <Suspense fallback={<TabLoadingSkeleton />}>
                  <ApiKeysTab />
                </Suspense>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Settings;

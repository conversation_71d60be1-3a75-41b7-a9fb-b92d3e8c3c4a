import React, { useState } from 'react';
import { UserRole } from '@prisma/client';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Mail, MoreHorizontal, Check, Clock, ShieldCheck, ShieldX } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { updateMemberRole, removeMember } from '@/lib/actions/team';
import { toast } from 'sonner';
import { formatTimeElapsed } from '@/lib/utils';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Define types for team member
interface TeamMember {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  profileImageUrl: string | null;
  role: UserRole;
  lastActive: Date | null;
  status: string;
  createdAt: Date;
}

interface TeamMembersListProps {
  members: TeamMember[];
  onRefresh: () => void;
}

export default function TeamMembersList({ members, onRefresh }: TeamMembersListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [newRole, setNewRole] = useState<UserRole>(UserRole.VIEWER);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter members based on search term
  const filteredMembers = members.filter(member => {
    const fullName = `${member.firstName || ''} ${member.lastName || ''}`.trim().toLowerCase();
    const email = member.email.toLowerCase();
    const search = searchTerm.toLowerCase();
    
    return fullName.includes(search) || email.includes(search);
  });

  // Get role badge
  const getRoleBadge = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return <Badge className="bg-blue-600">Admin</Badge>;
      case UserRole.EDITOR:
        return (
          <Badge
            variant="outline"
            className="text-green-600 border-green-200 bg-green-50"
          >
            Editor
          </Badge>
        );
      case UserRole.VIEWER:
        return (
          <Badge
            variant="outline"
            className="text-amber-600 border-amber-200 bg-amber-50"
          >
            Viewer
          </Badge>
        );
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Check className="h-4 w-4 text-green-500" />;
      case "INACTIVE":
        return <Clock className="h-4 w-4 text-amber-500" />;
      case "SUSPENDED":
        return <ShieldX className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  // Handle role change
  const handleRoleChange = async () => {
    if (!selectedMember || !newRole) return;
    
    setIsSubmitting(true);
    try {
      const result = await updateMemberRole(selectedMember.id, newRole);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(`Role updated to ${newRole}`);
        onRefresh();
      }
    } finally {
      setIsSubmitting(false);
      setIsRoleDialogOpen(false);
    }
  };

  // Handle member removal
  const handleRemoveMember = async () => {
    if (!selectedMember) return;
    
    setIsSubmitting(true);
    try {
      const result = await removeMember(selectedMember.id);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Member removed from organization');
        onRefresh();
      }
    } finally {
      setIsSubmitting(false);
      setIsRemoveDialogOpen(false);
    }
  };

  return (
    <>
      <div className="p-6 border-b border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-r from-blue-50/50 via-white to-cyan-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20 rounded-t-3xl">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 rounded-xl bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 shadow-sm">
            <ShieldCheck className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Team Members</h3>
            <p className="text-gray-600 dark:text-gray-400 font-medium text-sm">Manage your team members and their roles</p>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-3 justify-between">
          <div className="relative w-full sm:w-72">
            <Search className="absolute left-3 top-3 h-4 w-4 text-blue-500 dark:text-blue-400" />
            <Input
              type="search"
              placeholder="Search members..."
              className="pl-10 w-full border-blue-200/50 dark:border-blue-700/50 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500/20"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </div>

      <div className="p-6 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10">
        <div className="overflow-x-auto">
          <div className="min-w-full bg-white/70 dark:bg-gray-800/70 rounded-lg border border-blue-200/50 dark:border-blue-700/50 overflow-hidden">
            <table className="w-full">
              <thead className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/50 dark:to-cyan-950/50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Last Active
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200/50 dark:divide-gray-700/50">
                {filteredMembers.map((member, index) => (
                  <tr key={member.id} className={`${index % 2 === 0 ? 'bg-white/50 dark:bg-gray-800/30' : 'bg-gray-50/50 dark:bg-gray-700/30'}`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Avatar className="h-10 w-10 mr-4 ring-2 ring-blue-200/50 dark:ring-blue-700/50">
                          <AvatarImage
                            src={member.profileImageUrl || `https://api.dicebear.com/7.x/avataaars/svg?seed=${member.email}`}
                            alt={`${member.firstName || ''} ${member.lastName || ''}`.trim() || member.email}
                          />
                          <AvatarFallback className="bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 text-blue-700 dark:text-blue-300 font-semibold">
                            {member.firstName?.[0] || ''}
                            {member.lastName?.[0] || ''}
                            {!member.firstName && !member.lastName ? member.email[0].toUpperCase() : ''}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-semibold text-gray-900 dark:text-gray-100">
                            {`${member.firstName || ''} ${member.lastName || ''}`.trim() || 'Unnamed User'}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {member.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getRoleBadge(member.role)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getStatusIcon(member.status)}
                        <span className="ml-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                          {member.status.charAt(0) + member.status.slice(1).toLowerCase()}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 font-medium">
                      {member.lastActive ? formatTimeElapsed(member.lastActive) : 'Never'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            size="icon"
                            className="h-8 w-8 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 shadow-md hover:shadow-lg transition-all duration-200 border border-gray-300 dark:border-gray-600 cursor-pointer"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg">
                      <DropdownMenuLabel className="text-gray-900 dark:text-gray-100">Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => window.location.href = `mailto:${member.email}`}
                        className="hover:bg-blue-50 dark:hover:bg-blue-950/30 cursor-pointer"
                      >
                        <Mail className="mr-2 h-4 w-4 text-blue-600 dark:text-blue-400" />
                        Email
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => {
                          setSelectedMember(member);
                          setNewRole(member.role);
                          setIsRoleDialogOpen(true);
                        }}
                        className="hover:bg-green-50 dark:hover:bg-green-950/30 cursor-pointer"
                      >
                        <ShieldCheck className="mr-2 h-4 w-4 text-green-600 dark:text-green-400" />
                        Change Role
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        className="text-red-600 hover:bg-red-50 dark:hover:bg-red-950/30 cursor-pointer"
                        onClick={() => {
                          setSelectedMember(member);
                          setIsRemoveDialogOpen(true);
                        }}
                      >
                        <ShieldX className="mr-2 h-4 w-4" />
                        Remove
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </td>
              </tr>
            ))}
                {filteredMembers.length === 0 && (
                  <tr>
                    <td colSpan={5} className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center gap-3">
                        <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-700">
                          <ShieldCheck className="w-6 h-6 text-gray-400" />
                        </div>
                        <p className="text-gray-600 dark:text-gray-400 font-medium">
                          {searchTerm
                            ? 'No members found matching your search.'
                            : 'No team members found. Invite some members to get started.'}
                        </p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Change Role Dialog */}
      <AlertDialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
        <AlertDialogContent className="bg-gradient-to-br from-white via-blue-50/10 to-cyan-50/10 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10 border border-blue-200/30 dark:border-blue-700/20">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-gray-900 dark:text-gray-100">Change Team Member Role</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600 dark:text-gray-400">
              This will change the role and permissions for {selectedMember?.firstName || selectedMember?.email}.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="py-4">
            <Select 
              value={newRole} 
              onValueChange={(value) => setNewRole(value as UserRole)}
            >
              <SelectTrigger className="border-blue-200/50 dark:border-blue-700/50 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500/20">
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={UserRole.ADMIN}>Admin</SelectItem>
                <SelectItem value={UserRole.EDITOR}>Editor</SelectItem>
                <SelectItem value={UserRole.VIEWER}>Viewer</SelectItem>
              </SelectContent>
            </Select>
            
            <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
              {newRole === UserRole.ADMIN && (
                <p>Admins can manage team members, subscription, and have full access to all features.</p>
              )}
              {newRole === UserRole.EDITOR && (
                <p>Editors can create and edit content but cannot manage team members or subscription settings.</p>
              )}
              {newRole === UserRole.VIEWER && (
                <p>Viewers have read-only access to content but cannot make any changes.</p>
              )}
            </div>
          </div>
          
          <AlertDialogFooter>
            <AlertDialogCancel 
              disabled={isSubmitting}
              className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRoleChange}
              disabled={isSubmitting}
              className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0"
            >
              {isSubmitting ? 'Updating...' : 'Update Role'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Remove Member Dialog */}
      <AlertDialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <AlertDialogContent className="bg-gradient-to-br from-white via-red-50/10 to-pink-50/10 dark:from-gray-800/50 dark:via-red-950/10 dark:to-pink-950/10 border border-red-200/30 dark:border-red-700/20">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-gray-900 dark:text-gray-100">Remove Team Member</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600 dark:text-gray-400">
              This will remove {selectedMember?.firstName || selectedMember?.email} from your organization. 
              They will lose access to all resources and data immediately.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel 
              disabled={isSubmitting}
              className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRemoveMember}
              disabled={isSubmitting}
              className="bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0"
            >
              {isSubmitting ? 'Removing...' : 'Remove Member'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 
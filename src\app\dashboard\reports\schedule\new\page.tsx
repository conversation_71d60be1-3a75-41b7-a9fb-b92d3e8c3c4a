import React from "react";
import { redirect } from "next/navigation";
import { auth } from "@clerk/nextjs/server";
import { getReportById, getReports } from "@/lib/actions/reports";
import { ScheduleReportForm } from "@/components/dashboard/reports/schedule-report-form";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Clock } from "lucide-react";

interface SearchParams {
  reportId?: string;
  frequency?: string;
}

export default async function NewSchedulePage(
  props: {
    searchParams: Promise<SearchParams>;
  }
) {
  const searchParams = await props.searchParams;
  const { userId } = await auth();

  if (!userId) {
    redirect("/sign-in");
  }

  // Access reportId from searchParams
  const reportId = searchParams?.reportId;
  const frequency = searchParams?.frequency || "weekly";

  // If no reportId is provided, show report selection UI
  if (!reportId) {
    try {
      const reports = await getReports();

      return (
        <DashboardLayout>
          <div className="p-6">
            <div className="flex items-center gap-4 mb-6">
              <Button asChild variant="subtle" size="icon">
                <Link href="/dashboard/reports">
                  <ArrowLeft className="h-5 w-5" />
                </Link>
              </Button>
              <div>
                <h1 className="text-2xl font-semibold">Select a Report to Schedule</h1>
                <p className="text-muted-foreground">Choose a report to set up automated delivery</p>
              </div>
            </div>

            {reports.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="flex flex-col items-center py-8">
                    <Clock className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium">No reports available</h3>
                    <p className="text-muted-foreground mt-2 mb-4">
                      You need to create a report before you can schedule it for automated delivery.
                    </p>
                    <Button asChild>
                      <Link href="/dashboard/reports/new">Create a Report</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <>
                <Card className="mb-6 bg-muted/50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-primary" />
                      <p className="text-sm">
                        Scheduled reports will be automatically generated and emailed to recipients on your chosen schedule.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {reports.map((report) => (
                    <Card key={report.id} className="overflow-hidden hover:shadow-md transition-shadow">
                      <CardHeader className="pb-2">
                        <CardTitle className="flex items-center gap-2">
                          {report.title || report.name || "Untitled Report"}
                        </CardTitle>
                        <CardDescription>
                          {(report.reportType || report.type || "Unknown").replace(/_/g, ' ')} • Created {new Date(report.createdAt).toLocaleDateString()}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                          {report.description || "No description provided"}
                        </p>
                        <Button asChild className="w-full">
                          <Link href={`/dashboard/reports/schedule/new?reportId=${report.id}&frequency=${frequency}`}>
                            <Clock className="mr-2 h-4 w-4" />
                            Schedule This Report
                          </Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </>
            )}
          </div>
        </DashboardLayout>
      );
    } catch {
      redirect("/dashboard/reports");
    }
  }

  try {
    const report = await getReportById(reportId);

    if (!report) {
      redirect("/dashboard/reports");
    }

    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="flex items-center gap-4 mb-6">
            <Button asChild variant="subtle" size="icon">
              <Link href="/dashboard/reports">
                <ArrowLeft className="h-5 w-5" />
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-semibold">Schedule Report</h1>
              <p className="text-muted-foreground">Set up automated delivery of your report</p>
            </div>
          </div>
          <div className="grid gap-8">
            <ScheduleReportForm report={report} initialFrequency={frequency} />
          </div>
        </div>
      </DashboardLayout>
    );
  } catch {
    redirect("/dashboard/reports");
  }
}
'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Check,
  CreditCard,
  Star,
  Shield,
} from 'lucide-react';
import PlanFeatures from './PlanFeatures';

interface Plan {
  id: number;
  name: string;
  description: string | null;
  price: string;
  interval: string | null;
  intervalCount: number | null;
  isUsageBased: boolean | null;
  productId: number;
  productName: string | null;
  paddlePriceId: string | null;
  stripeProductId?: string | null;
  stripePriceId?: string | null;
  features?: string | null;
  sort: number | null;
}

interface PlanItemProps {
  plan: Plan;
  index: number;
  isCurrentPlan: boolean;
  formatPrice: (price: string) => string;
  handlePlanUpgrade: (priceId: string) => void;
  isLoading: boolean;
  planType: 'monthly' | 'yearly';
}

const PlanItem: React.FC<PlanItemProps> = ({
  plan,
  index,
  isCurrentPlan,
  formatPrice,
  handlePlanUpgrade,
  isLoading,
  planType,
}) => {
  // Use Dashboard color schemes based on index
  const getColorScheme = (index: number) => {
    const schemes = [
      {
        // Blue/Cyan - Cash Flow theme
        card: 'border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10',
        header: 'border-b border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-r from-blue-50/50 via-white to-cyan-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-cyan-950/20 rounded-t-3xl',
        content: 'p-6 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-cyan-950/10',
        icon: 'p-2 rounded-xl bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/40 dark:to-cyan-900/40 shadow-sm',
        button: 'w-full bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30 border-blue-200/50 dark:border-blue-700/50 hover:from-blue-100 hover:to-cyan-100 dark:hover:from-blue-950/50 dark:hover:to-cyan-950/50 text-blue-700 dark:text-blue-300',
        badge: 'bg-gradient-to-r from-blue-500 to-cyan-600 text-white',
        ring: 'ring-2 ring-blue-500/50'
      },
      {
        // Purple/Indigo - Subscription theme
        card: 'border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10',
        header: 'border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/50 via-white to-indigo-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 rounded-t-3xl',
        content: 'p-6 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10',
        icon: 'p-2 rounded-xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40 shadow-sm',
        button: 'w-full bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950/30 dark:to-indigo-950/30 border-purple-200/50 dark:border-purple-700/50 hover:from-purple-100 hover:to-indigo-100 dark:hover:from-purple-950/50 dark:hover:to-indigo-950/50 text-purple-700 dark:text-purple-300',
        badge: 'bg-gradient-to-r from-purple-500 to-indigo-600 text-white',
        ring: 'ring-2 ring-purple-500/50'
      },
      {
        // Teal/Emerald - Vendor Distribution theme
        card: 'border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-br from-white via-teal-50/20 to-emerald-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-emerald-950/10',
        header: 'border-b border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-r from-teal-50/50 via-white to-emerald-50/50 dark:from-teal-950/20 dark:via-gray-800/50 dark:to-emerald-950/20 rounded-t-3xl',
        content: 'p-6 bg-gradient-to-br from-white via-teal-50/20 to-emerald-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-emerald-950/10',
        icon: 'p-2 rounded-xl bg-gradient-to-br from-teal-100 to-emerald-100 dark:from-teal-900/40 dark:to-emerald-900/40 shadow-sm',
        button: 'w-full bg-gradient-to-r from-teal-50 to-emerald-50 dark:from-teal-950/30 dark:to-emerald-950/30 border-teal-200/50 dark:border-teal-700/50 hover:from-teal-100 hover:to-emerald-100 dark:hover:from-teal-950/50 dark:hover:to-emerald-950/50 text-teal-700 dark:text-teal-300',
        badge: 'bg-gradient-to-r from-teal-500 to-emerald-600 text-white',
        ring: 'ring-2 ring-teal-500/50'
      }
    ];
    return schemes[index % 3];
  };

  const colors = getColorScheme(index);
  const cardClasses = `relative ${colors.card} ${isCurrentPlan ? colors.ring : ''} transition-all duration-300 hover:shadow-lg`;

  const getIcon = () => {
    if (plan.productName?.includes('Pro')) {
      return <Star className="w-5 h-5 text-amber-600 dark:text-amber-400" />;
    }
    if (plan.productName?.includes('Enterprise')) {
      return <Shield className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
    }
    return <CreditCard className="w-5 h-5 text-gray-600 dark:text-gray-400" />;
  };

  return (
    <Card className={cardClasses}>
      {isCurrentPlan && (
        <div className="absolute -top-2 -right-2">
          <div className={`${colors.badge} text-xs font-semibold py-1 px-3 rounded-full shadow-lg`}>
            Current Plan
          </div>
        </div>
      )}
      
      <CardHeader className={colors.header}>
        <div className="flex items-center gap-3">
          <div className={colors.icon}>
            {getIcon()}
          </div>
          <div>
            <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">
              {plan.productName || plan.name}
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
              {plan.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className={colors.content}>
        <div className="space-y-6">
          {/* Price Display */}
          <div className="text-center">
            <div className="text-4xl font-bold text-gray-900 dark:text-gray-100">
              {formatPrice(plan.price)}
              <span className="text-lg font-normal text-gray-600 dark:text-gray-400">
                /{plan.interval || 'month'}
              </span>
            </div>
            {planType === 'yearly' && (
              <p className="text-sm text-green-600 dark:text-green-400 font-medium mt-1">
                Save 20% compared to monthly
              </p>
            )}
          </div>

          {/* Action Button */}
          {isCurrentPlan ? (
            <Button className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg" disabled>
              <Check className="mr-2 h-4 w-4" />
              Current Plan
            </Button>
          ) : (
            <Button
              variant="outline"
              className={`${colors.button} shadow-lg hover:shadow-xl transition-all duration-300`}
              onClick={() => plan.paddlePriceId && handlePlanUpgrade(plan.paddlePriceId)}
              disabled={isLoading}
            >
              {isLoading
                ? 'Processing...'
                : plan.productName?.includes('Enterprise')
                  ? 'Contact Sales'
                  : `Choose ${plan.productName || plan.name}`}
            </Button>
          )}

          {/* Plan Features */}
          <PlanFeatures plan={plan} colorScheme={colors} />
        </div>
      </CardContent>
    </Card>
  );
};

export default PlanItem;

'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { toast } from 'sonner';
import { UserProfile } from '@/lib/services/user-service';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { 
  MessageCircle, 
  Mail, 
  Phone, 
  Users,
  Send,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

// Use environment variable for backend base URL
const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;
if (!backendUrl) {
  throw new Error('NEXT_PUBLIC_BACKEND_URL environment variable is not configured');
}

interface SupportFormProps {
  className?: string;
}

export default function SupportForm({ className }: SupportFormProps) {
  const { user } = useUser();
  const [form, setForm] = useState({
    name: '',
    email: '',
    phone_number: '',
    message: '',
  });
  const [dbUser, setDbUser] = useState<UserProfile | null>(null);
  const [userLoading, setUserLoading] = useState(true);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // Auto-fill form fields when user data is available
  useEffect(() => {
    if (user) {
      const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
      const userEmail = user.primaryEmailAddress?.emailAddress || '';

      setForm((prev) => ({
        ...prev,
        name: fullName || prev.name,
        email: userEmail || prev.email,
      }));
    }
  }, [user]);

  useEffect(() => {
    async function fetchDbUser() {
      setUserLoading(true);
      try {
        const res = await fetch('/api/user/profile');
        if (!res.ok) throw new Error('Failed to fetch user');
        const data = await res.json();
        setDbUser(data);
      } catch {
        setDbUser(null);
      } finally {
        setUserLoading(false);
      }
    }
    fetchDbUser();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!form.name || !form.email || !form.phone_number || !form.message) {
      setError('Please fill in all fields.');
      return;
    }
    if (!dbUser?.id) {
      setError('User not authenticated.');
      return;
    }
    setLoading(true);
    setError('');
    setSubmitted(false);
    try {
      const res = await fetch(`${backendUrl}/api/v1/help-support/${dbUser.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: form.name,
          phone_number: form.phone_number,
          email: form.email,
          message: form.message,
        }),
      });
      if (!res.ok) {
        throw new Error('Failed to submit.');
      }
      setSubmitted(true);
      setForm({ name: '', email: '', phone_number: '', message: '' });
      toast.success('Your message has been submitted successfully!');
    } catch {
      setError('Submission failed. Please try again.');
      toast.error('Failed to submit your message. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (userLoading) {
    return (
      <Card className="border-blue-200/30 dark:border-blue-700/20">
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-12">
            <div className="flex flex-col items-center gap-3">
              <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
              <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">Loading form...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`border-teal-200/30 dark:border-teal-700/20 ${className}`}>
      <CardHeader className="border-b border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-r from-teal-50/50 via-white to-emerald-50/50 dark:from-teal-950/20 dark:via-gray-800/50 dark:to-emerald-950/20 rounded-t-3xl p-6">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-xl bg-gradient-to-br from-teal-100 to-emerald-100 dark:from-teal-900/40 dark:to-emerald-900/40 shadow-sm">
            <MessageCircle className="w-5 h-5 text-teal-600 dark:text-teal-400" />
          </div>
          <div>
            <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Contact Support</CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
              Send us a message and we'll respond within 2-4 hours
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6 bg-gradient-to-br from-white via-teal-50/20 to-emerald-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-emerald-950/10">
        <form className="space-y-6" onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                <Users className="w-4 h-4" />
                Name
              </Label>
              <Input
                id="name"
                type="text"
                name="name"
                placeholder="Your full name"
                value={form.name}
                onChange={handleChange}
                required
                className="border-blue-200/50 dark:border-blue-700/50 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500/20"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Email
              </Label>
              <Input
                id="email"
                type="email"
                name="email"
                placeholder="<EMAIL>"
                value={form.email}
                onChange={handleChange}
                required
                className="border-blue-200/50 dark:border-blue-700/50 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500/20"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone_number" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
              <Phone className="w-4 h-4" />
              Phone Number
            </Label>
            <Input
              id="phone_number"
              type="tel"
              name="phone_number"
              placeholder="+****************"
              value={form.phone_number}
              onChange={handleChange}
              required
              className="border-blue-200/50 dark:border-blue-700/50 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500/20"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="message" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
              <MessageCircle className="w-4 h-4" />
              Message
            </Label>
            <Textarea
              id="message"
              name="message"
              placeholder="Describe your issue or question in detail. The more information you provide, the better we can help you..."
              value={form.message}
              onChange={handleChange}
              required
              className="min-h-[140px] border-blue-200/50 dark:border-blue-700/50 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500/20 resize-none"
            />
          </div>

          {/* Error and Success Messages */}
          {error && (
            <div className="flex items-center gap-2 text-red-700 dark:text-red-400 text-sm bg-red-50 dark:bg-red-950/20 p-4 rounded-lg border border-red-200 dark:border-red-800/50">
              <AlertCircle className="w-4 h-4 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          {submitted && !error && (
            <div className="flex items-center gap-2 text-green-700 dark:text-green-400 text-sm bg-green-50 dark:bg-green-950/20 p-4 rounded-lg border border-green-200 dark:border-green-800/50">
              <CheckCircle className="w-4 h-4 flex-shrink-0" />
              <span>Thank you! Your message has been submitted successfully. We'll get back to you soon.</span>
            </div>
          )}

          <Button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 py-3 cursor-pointer"
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Submitting...
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                Send Message
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}

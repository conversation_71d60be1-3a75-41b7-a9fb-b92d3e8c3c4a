'use client';

import { UserProfile } from '@clerk/nextjs';
import { useTheme } from 'next-themes';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

const SecurityTab = () => {
  const { theme, resolvedTheme } = useTheme();

  // Determine the current theme (resolvedTheme handles 'system' theme)
  const currentTheme = resolvedTheme || theme;

  return (
    <div className="space-y-4">
      <Card className="border-blue-200/30 dark:border-blue-700/20">
        <CardHeader className="border-b border-blue-200/30 dark:border-blue-700/20 bg-gradient-to-r from-blue-50/50 via-white to-purple-50/50 dark:from-blue-950/20 dark:via-gray-800/50 dark:to-purple-950/20 rounded-t-3xl p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/40 dark:to-purple-900/40 shadow-sm">
              <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Account Management</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                Manage your profile, security preferences, and authentication methods
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6 bg-gradient-to-br from-white via-blue-50/20 to-purple-50/20 dark:from-gray-800/50 dark:via-blue-950/10 dark:to-purple-950/10">
          <div className="clerk-user-profile">
            <UserProfile
              routing="hash"
              appearance={{
                variables: {
                  colorPrimary:
                    currentTheme === 'dark' ? 'white' : 'black',
                  colorText:
                    currentTheme === 'dark' ? 'white' : 'black',
                },
                elements: {
                  rootBox: {
                    boxShadow: 'none',
                    width: '100%',
                  },
                  card: {
                    border: 'none',
                    boxShadow: 'none',
                    width: '100%',
                  },
                  cardBox: {
                    width: '100%',
                    backgroundColor: 'transparent',
                    border: 'none',
                    boxShadow: 'none',
                  },
                  scrollBox: {
                    backgroundColor:
                      currentTheme === 'dark'
                        ? 'transparent'
                        : 'white',
                    width: '100%',
                  },
                  navbar: {
                    background:
                      currentTheme === 'dark'
                        ? 'transparent'
                        : 'white',
                  },
                },
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SecurityTab;

"use client"

import { AnimatePresence, motion } from "motion/react"
import { useState } from "react"
import { useWindowSize } from "usehooks-ts"

import type { UISuggestion } from "@/lib/ai/editor/suggestions"

import { CrossIcon, MessageIcon } from "./icons"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import type { ArtifactKind } from "./artifact"

export const Suggestion = ({
  suggestion,
  onApply,
  artifactKind,
}: {
  suggestion: UISuggestion
  onApply: () => void
  artifactKind: ArtifactKind
}) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const { width: windowWidth } = useWindowSize()

  return (
    <AnimatePresence>
      {!isExpanded ? (
        <motion.div
          className={cn(
            "cursor-pointer text-indigo-500 dark:text-indigo-400 p-1.5 hover:bg-background/80 rounded-full",
            {
              "absolute -right-8": artifactKind === "text",
              "sticky top-0 right-4": artifactKind === "code",
            },
          )}
          onClick={() => {
            setIsExpanded(true)
          }}
          whileHover={{ scale: 1.1 }}
        >
          <MessageIcon size={windowWidth && windowWidth < 768 ? 16 : 14} />
        </motion.div>
      ) : (
        <motion.div
          key={suggestion.id}
          className="absolute bg-background p-4 flex flex-col gap-3 rounded-2xl border border-border/60 text-sm w-64 shadow-xl z-50 -right-12 md:-right-16 font-sans"
          transition={{ type: "spring", stiffness: 500, damping: 30 }}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: -20 }}
          exit={{ opacity: 0, y: -10 }}
          whileHover={{ scale: 1.05 }}
        >
          <div className="flex flex-row items-center justify-between">
            <div className="flex flex-row items-center gap-2">
              <div className="size-4 bg-indigo-500 rounded-full" />
              <div className="font-medium">Assistant</div>
            </div>
            <button
              type="button"
              className="text-xs text-gray-500 cursor-pointer hover:bg-muted rounded-full p-1"
              onClick={() => {
                setIsExpanded(false)
              }}
            >
              <CrossIcon size={12} />
            </button>
          </div>
          <div>{suggestion.description}</div>
          <Button
            variant="outline"
            className="w-fit py-1.5 px-3 rounded-full border-indigo-500/30 hover:border-indigo-500/60 text-indigo-600 dark:text-indigo-400"
            onClick={onApply}
          >
            Apply
          </Button>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

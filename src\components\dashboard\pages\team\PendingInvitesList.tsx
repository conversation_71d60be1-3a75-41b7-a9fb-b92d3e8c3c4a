import React, { useState } from 'react';
import { UserRole } from '@prisma/client';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { resendInvite, cancelInvite } from '@/lib/actions/team';
import { toast } from 'sonner';
import { formatDate } from '@/lib/utils';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Mail, Clock, RefreshCw, X, Users } from 'lucide-react';

// Define types for invites
interface Invite {
  id: string;
  email: string;
  role: UserRole;
  invitedBy: string;
  invitedOn: string;
  expiresAt: Date;
}

interface PendingInvitesListProps {
  invites: Invite[];
  onRefresh: () => void;
}

export default function PendingInvitesList({ invites, onRefresh }: PendingInvitesListProps) {
  const [selectedInvite, setSelectedInvite] = useState<Invite | null>(null);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get role badge
  const getRoleBadge = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return <Badge className="bg-gradient-to-r from-blue-500 to-cyan-600 text-white border-0">Admin</Badge>;
      case UserRole.EDITOR:
        return (
          <Badge className="bg-gradient-to-r from-green-500 to-emerald-600 text-white border-0">
            Editor
          </Badge>
        );
      case UserRole.VIEWER:
        return (
          <Badge className="bg-gradient-to-r from-amber-500 to-orange-600 text-white border-0">
            Viewer
          </Badge>
        );
      default:
        return <Badge className="bg-gradient-to-r from-gray-500 to-gray-600 text-white border-0">{role}</Badge>;
    }
  };

  // Handle resend invite
  const handleResendInvite = async (inviteId: string) => {
    setIsSubmitting(true);
    try {
      const result = await resendInvite(inviteId);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Invitation resent successfully');
        onRefresh();
      }
    } catch {
      toast.error('Failed to resend invitation');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel invite
  const handleCancelInvite = async () => {
    if (!selectedInvite) return;
    
    setIsSubmitting(true);
    try {
      const result = await cancelInvite(selectedInvite.id);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success('Invitation cancelled');
        onRefresh();
      }
    } catch {
      toast.error('Failed to cancel invitation');
    } finally {
      setIsSubmitting(false);
      setIsCancelDialogOpen(false);
    }
  };

  if (invites.length === 0) {
    return (
      <div className="p-6 border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/50 via-white to-indigo-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 rounded-t-3xl">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 rounded-xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40 shadow-sm">
            <Mail className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Pending Invites</h3>
            <p className="text-gray-600 dark:text-gray-400 font-medium text-sm">Manage your pending invitations</p>
          </div>
        </div>
        <Card className="shadow-sm border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10">
          <CardContent className="p-12 text-center">
            <div className="flex flex-col items-center gap-3">
              <div className="p-3 rounded-full bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40">
                <Mail className="w-6 h-6 text-purple-400" />
              </div>
              <p className="text-gray-600 dark:text-gray-400 font-medium">
                No pending invitations found.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <div className="p-6 border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/50 via-white to-indigo-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-indigo-950/20 rounded-t-3xl">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 rounded-xl bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/40 dark:to-indigo-900/40 shadow-sm">
            <Mail className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">Pending Invites</h3>
            <p className="text-gray-600 dark:text-gray-400 font-medium text-sm">Manage your pending invitations</p>
          </div>
        </div>
      </div>

      <div className="p-6 bg-gradient-to-br from-white via-purple-50/20 to-indigo-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-indigo-950/10">
        <div className="overflow-x-auto">
          <div className="min-w-full bg-white/70 dark:bg-gray-800/70 rounded-lg border border-purple-200/50 dark:border-purple-700/50 overflow-hidden">
            <table className="w-full">
              <thead className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950/50 dark:to-indigo-950/50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Invited By
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-4 text-right text-xs font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200/50 dark:divide-gray-700/50">
                {invites.map((invite, index) => (
                  <tr key={invite.id} className={`${index % 2 === 0 ? 'bg-white/50 dark:bg-gray-800/30' : 'bg-gray-50/50 dark:bg-gray-700/30'}`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900 dark:text-gray-100">{invite.email}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 font-medium">
                        Expires {formatDate(invite.expiresAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getRoleBadge(invite.role)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 font-medium">
                      {invite.invitedBy}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 font-medium">
                      {invite.invitedOn}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          onClick={() => handleResendInvite(invite.id)}
                          disabled={isSubmitting}
                          className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0"
                          size="sm"
                        >
                          <RefreshCw className="mr-2 h-3 w-3" />
                          Resend
                        </Button>
                        <Button
                          onClick={() => {
                            setSelectedInvite(invite);
                            setIsCancelDialogOpen(true);
                          }}
                          disabled={isSubmitting}
                          className="bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0"
                          size="sm"
                        >
                          <X className="mr-2 h-3 w-3" />
                          Cancel
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Cancel Invite Dialog */}
      <AlertDialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
        <AlertDialogContent className="bg-gradient-to-br from-white via-red-50/10 to-pink-50/10 dark:from-gray-800/50 dark:via-red-950/10 dark:to-pink-950/10 border border-red-200/30 dark:border-red-700/20">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-gray-900 dark:text-gray-100">Cancel Invitation</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600 dark:text-gray-400">
              This will cancel the invitation sent to {selectedInvite?.email}.
              They will no longer be able to join your organization using this invite.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel 
              disabled={isSubmitting}
              className="border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Keep Invite
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleCancelInvite}
              disabled={isSubmitting}
              className="bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg hover:shadow-xl transition-all duration-200 border-0"
            >
              {isSubmitting ? 'Cancelling...' : 'Cancel Invite'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 
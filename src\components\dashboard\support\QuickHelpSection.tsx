'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { BookOpen, Video, MessageCircle, FileText } from 'lucide-react';

// Quick help items
const quickHelpItems = [
  {
    icon: BookOpen,
    title: "Documentation",
    description: "Browse our comprehensive guides and tutorials",
    action: "View Docs",
    color: "blue",
    href: "/docs"
  },
  {
    icon: Video,
    title: "Video Tutorials",
    description: "Watch step-by-step video guides",
    action: "Watch Videos",
    color: "purple",
    href: "/tutorials"
  },
  {
    icon: MessageCircle,
    title: "Live Chat",
    description: "Chat with our support team in real-time",
    action: "Start Chat",
    color: "teal",
    href: "#chat"
  },
  {
    icon: FileText,
    title: "FAQ",
    description: "Find answers to frequently asked questions",
    action: "Browse FAQ",
    color: "green",
    href: "/faq"
  }
];

interface QuickHelpSectionProps {
  className?: string;
}

export default function QuickHelpSection({ className }: QuickHelpSectionProps) {
  const handleQuickAction = (href: string, title: string) => {
    if (href === "#chat") {
      // Handle live chat opening logic here
      console.log("Opening live chat...");
      return;
    }
    
    // For other links, you can navigate or open in new tab
    if (href.startsWith('/')) {
      window.location.href = href;
    } else {
      window.open(href, '_blank');
    }
  };

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {quickHelpItems.map((item, index) => {
        const Icon = item.icon;
        const colorClasses = {
          blue: "from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700",
          purple: "from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700",
          teal: "from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700",
          green: "from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700"
        };
        
        return (
          <Card key={index} className="border-gray-200/50 dark:border-gray-700/50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3 mb-3">
                <div className={`p-2 rounded-lg bg-gradient-to-br ${colorClasses[item.color as keyof typeof colorClasses]} shadow-sm`}>
                  <Icon className="w-4 h-4 text-white" />
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm">{item.title}</h3>
              </div>
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">{item.description}</p>
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full text-xs hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                onClick={() => handleQuickAction(item.href, item.title)}
              >
                {item.action}
              </Button>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}

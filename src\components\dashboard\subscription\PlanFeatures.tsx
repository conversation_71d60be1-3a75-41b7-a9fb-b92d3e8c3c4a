'use client';

import React from 'react';
import { Check } from 'lucide-react';

interface Plan {
  id: number;
  name: string;
  description: string | null;
  price: string;
  interval: string | null;
  intervalCount: number | null;
  isUsageBased: boolean | null;
  productId: number;
  productName: string | null;
  paddlePriceId: string | null;
  stripeProductId?: string | null;
  stripePriceId?: string | null;
  features?: string | null;
  sort: number | null;
}

interface ColorScheme {
  card: string;
  header: string;
  content: string;
  icon: string;
  button: string;
  badge: string;
  ring: string;
}

interface PlanFeaturesProps {
  plan: Plan;
  colorScheme: ColorScheme;
}

const PlanFeatures: React.FC<PlanFeaturesProps> = ({ plan, colorScheme }) => {
  const getFeatures = (plan: Plan) => {
    const features = [];
    
    // Invoice uploads
    const invoiceLimit = plan.productName?.includes('Starter') ? '10'
      : plan.productName?.includes('Business') ? '100'
      : plan.productName?.includes('Enterprise') ? '1000'
      : '50';
    
    features.push(`${invoiceLimit} invoice uploads/${plan.interval || 'month'}`);
    
    // Chat messages
    const chatLimit = plan.productName?.includes('Starter') ? '5'
      : plan.productName?.includes('Business') ? '50'
      : plan.productName?.includes('Enterprise') ? '500'
      : '25';
    
    features.push(`${chatLimit} chat messages/${plan.interval || 'month'}`);
    
    // OCR extraction
    const ocrLevel = plan.productName?.includes('Basic') ? 'Basic'
      : plan.productName?.includes('Pro') ? 'Advanced'
      : plan.productName?.includes('Starter') ? 'Limited'
      : 'Premium';
    
    features.push(`${ocrLevel} OCR extraction`);
    
    // Analytics dashboard
    const analyticsLevel = plan.productName?.includes('Basic') ? 'Basic'
      : plan.productName?.includes('Pro') ? 'Advanced'
      : plan.productName?.includes('Starter') ? 'Basic'
      : 'Premium';
    
    features.push(`${analyticsLevel} analytics dashboard`);
    
    // Support
    const supportLevel = plan.productName?.includes('Enterprise') ? 'Priority'
      : plan.productName?.includes('Pro') ? 'Priority'
      : 'Email';
    
    features.push(`${supportLevel} support`);
    
    return features;
  };

  const features = getFeatures(plan);

  return (
    <div className="border-t border-gray-200/30 dark:border-gray-700/20 pt-4">
      <div className="space-y-3">
        {features.map((feature, index) => (
          <div key={index} className="flex items-center">
            <Check className="h-4 w-4 text-green-600 dark:text-green-400 mr-3 flex-shrink-0" />
            <span className="text-gray-600 dark:text-gray-400 font-medium text-sm">
              {feature}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PlanFeatures;

'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  DollarSign,
} from 'lucide-react';
import { format } from 'date-fns';

interface BillingTransaction {
  description?: string;
  date?: string;
  amount?: string;
  status?: string;
}

interface BillingHistoryCardProps {
  billingHistory: BillingTransaction[];
  billingLoading: boolean;
}

const BillingHistoryCard: React.FC<BillingHistoryCardProps> = ({
  billingHistory,
  billingLoading,
}) => {
  return (
    <Card className="border-indigo-200/30 dark:border-indigo-700/20 bg-gradient-to-br from-white via-indigo-50/20 to-purple-50/20 dark:from-gray-800/50 dark:via-indigo-950/10 dark:to-purple-950/10">
      <CardHeader className="border-b border-indigo-200/30 dark:border-indigo-700/20 bg-gradient-to-r from-indigo-50/50 via-white to-purple-50/50 dark:from-indigo-950/20 dark:via-gray-800/50 dark:to-purple-950/20 rounded-t-3xl">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-xl bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900/40 dark:to-purple-900/40 shadow-sm">
            <FileText className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
          </div>
          <div>
            <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Billing History</CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
              View your past transactions and invoices
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6 bg-gradient-to-br from-white via-indigo-50/20 to-purple-50/20 dark:from-gray-800/50 dark:via-indigo-950/10 dark:to-purple-950/10">
        {billingLoading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse space-y-3">
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            ))}
          </div>
        ) : billingHistory.length > 0 ? (
          <div className="space-y-4">
            {billingHistory.map((transaction, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 bg-white/70 dark:bg-gray-800/70 border border-indigo-200/50 dark:border-indigo-700/50 rounded-lg hover:bg-indigo-50/50 dark:hover:bg-indigo-950/20 transition-colors"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-900/40 dark:to-purple-900/40 rounded-full flex items-center justify-center shadow-sm">
                      <DollarSign className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                      {transaction.description ||
                        'Subscription Payment'}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                      {transaction.date
                        ? format(
                          new Date(transaction.date),
                          'MMM dd, yyyy'
                        )
                        : 'N/A'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                    {transaction.amount || 'N/A'}
                  </p>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={
                        transaction.status === 'Paid'
                          ? 'default'
                          : 'secondary'
                      }
                      className="text-xs bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/40 dark:to-emerald-900/40 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800/50"
                    >
                      {transaction.status || 'Unknown'}
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="flex flex-col items-center gap-4">
              <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-700">
                <FileText className="w-6 h-6 text-gray-400" />
              </div>
              <p className="text-gray-600 dark:text-gray-400 font-medium">
                No billing history available
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BillingHistoryCard;

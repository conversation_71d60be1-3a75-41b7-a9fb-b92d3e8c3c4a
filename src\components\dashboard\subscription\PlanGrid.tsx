'use client';

import React from 'react';
import PlanItem from './PlanItem';

interface Plan {
  id: number;
  name: string;
  description: string | null;
  price: string;
  interval: string | null;
  intervalCount: number | null;
  isUsageBased: boolean | null;
  productId: number;
  productName: string | null;
  paddlePriceId: string | null;
  stripeProductId?: string | null;
  stripePriceId?: string | null;
  features?: string | null;
  sort: number | null;
}

interface PlanGridProps {
  plans: Plan[];
  currentPlan: Plan | null | undefined;
  formatPrice: (price: string) => string;
  handlePlanUpgrade: (priceId: string) => void;
  isLoading: boolean;
  planType: 'monthly' | 'yearly';
}

const PlanGrid: React.FC<PlanGridProps> = ({
  plans,
  currentPlan,
  formatPrice,
  handlePlanUpgrade,
  isLoading,
  planType,
}) => {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {plans.map((plan, index) => (
        <PlanItem
          key={plan.id}
          plan={plan}
          index={index}
          isCurrentPlan={currentPlan?.id === plan.id}
          formatPrice={formatPrice}
          handlePlanUpgrade={handlePlanUpgrade}
          isLoading={isLoading}
          planType={planType}
        />
      ))}
    </div>
  );
};

export default PlanGrid;

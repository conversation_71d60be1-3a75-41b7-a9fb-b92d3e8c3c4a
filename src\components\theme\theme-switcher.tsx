"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { <PERSON><PERSON><PERSON>, <PERSON>, Sun } from "lucide-react";
import { motion } from "motion/react";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

const ThemeSwitcher = () => {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  // useEffect only runs on the client, so now we can safely show the UI
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const ICON_SIZE = 16;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="subtle" 
          size="icon"
          className="relative rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 hover:bg-white dark:hover:bg-gray-700 hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-purple-500/10 transition-all duration-300 hover:scale-105 active:scale-95"
        >
          {theme === "light" ? (
            <motion.div
              initial={{ rotate: -30, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="relative"
            >
              <Sun
                size={ICON_SIZE}
                className="text-amber-500 drop-shadow-sm"
              />
              <div className="absolute inset-0 rounded-full bg-amber-500/20 blur-sm animate-pulse" />
            </motion.div>
          ) : theme === "dark" ? (
            <motion.div
              initial={{ rotate: 30, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="relative"
            >
              <Moon
                size={ICON_SIZE}
                className="text-blue-400 drop-shadow-sm"
              />
              <div className="absolute inset-0 rounded-full bg-blue-400/20 blur-sm animate-pulse" />
            </motion.div>
          ) : (
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="relative"
            >
              <Laptop
                size={ICON_SIZE}
                className="text-gray-600 dark:text-gray-300 drop-shadow-sm"
              />
              <div className="absolute inset-0 rounded-full bg-gray-500/20 blur-sm animate-pulse" />
            </motion.div>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        className="w-44 p-3 mt-2 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-xl shadow-black/5 dark:shadow-black/20" 
        align="end"
      >
        <DropdownMenuRadioGroup
          value={theme}
          onValueChange={(e: string) => setTheme(e)}
        >
          <DropdownMenuRadioItem 
            className="flex items-center gap-3 rounded-xl cursor-pointer px-3 py-2.5 my-1 hover:bg-amber-50 dark:hover:bg-amber-900/20 focus:bg-amber-100 dark:focus:bg-amber-900/30 transition-all duration-200 data-[state=checked]:bg-amber-100 dark:data-[state=checked]:bg-amber-900/40 data-[state=checked]:text-amber-700 dark:data-[state=checked]:text-amber-300 group" 
            value="light"
          >
            <div className="relative">
              <Sun size={ICON_SIZE} className="text-amber-500 group-hover:text-amber-600 transition-colors" />
              <div className="absolute inset-0 rounded-full bg-amber-500/10 blur-sm opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
            <span className="font-medium">Light</span>
            <div className="ml-auto w-2 h-2 rounded-full bg-amber-500 opacity-0 data-[state=checked]:opacity-100 transition-opacity" />
          </DropdownMenuRadioItem>
          
          <DropdownMenuRadioItem 
            className="flex items-center gap-3 rounded-xl cursor-pointer px-3 py-2.5 my-1 hover:bg-blue-50 dark:hover:bg-blue-900/20 focus:bg-blue-100 dark:focus:bg-blue-900/30 transition-all duration-200 data-[state=checked]:bg-blue-100 dark:data-[state=checked]:bg-blue-900/40 data-[state=checked]:text-blue-700 dark:data-[state=checked]:text-blue-300 group" 
            value="dark"
          >
            <div className="relative">
              <Moon size={ICON_SIZE} className="text-blue-400 group-hover:text-blue-500 transition-colors" />
              <div className="absolute inset-0 rounded-full bg-blue-400/10 blur-sm opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
            <span className="font-medium">Dark</span>
            <div className="ml-auto w-2 h-2 rounded-full bg-blue-400 opacity-0 data-[state=checked]:opacity-100 transition-opacity" />
          </DropdownMenuRadioItem>
          
          <DropdownMenuRadioItem 
            className="flex items-center gap-3 rounded-xl cursor-pointer px-3 py-2.5 my-1 hover:bg-gray-50 dark:hover:bg-gray-700/50 focus:bg-gray-100 dark:focus:bg-gray-700/70 transition-all duration-200 data-[state=checked]:bg-gray-100 dark:data-[state=checked]:bg-gray-700/80 data-[state=checked]:text-gray-700 dark:data-[state=checked]:text-gray-200 group" 
            value="system"
          >
            <div className="relative">
              <Laptop size={ICON_SIZE} className="text-gray-600 dark:text-gray-300 group-hover:text-gray-700 dark:group-hover:text-gray-200 transition-colors" />
              <div className="absolute inset-0 rounded-full bg-gray-500/10 blur-sm opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
            <span className="font-medium">System</span>
            <div className="ml-auto w-2 h-2 rounded-full bg-gray-500 opacity-0 data-[state=checked]:opacity-100 transition-opacity" />
          </DropdownMenuRadioItem>
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export { ThemeSwitcher };
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { 
  Mail, 
  Clock, 
  Zap, 
  HelpCircle, 
  CheckCircle 
} from 'lucide-react';

interface SupportSidebarProps {
  className?: string;
}

export default function SupportSidebar({ className }: SupportSidebarProps) {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Contact Information */}
      <Card className="border-teal-200/30 dark:border-teal-700/20">
        <CardHeader className="border-b border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-r from-teal-50/50 via-white to-blue-50/50 dark:from-teal-950/20 dark:via-gray-800/50 dark:to-blue-950/20 rounded-t-3xl p-4">
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-gradient-to-br from-teal-100 to-blue-100 dark:from-teal-900/40 dark:to-blue-900/40">
              <HelpCircle className="w-4 h-4 text-teal-600 dark:text-teal-400" />
            </div>
            <CardTitle className="text-lg font-bold text-gray-900 dark:text-gray-100">Contact Info</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-4 bg-gradient-to-br from-white via-teal-50/20 to-blue-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-blue-950/10 space-y-4">
          <div className="flex items-center gap-3 p-3 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-teal-200/50 dark:border-teal-700/50">
            <Mail className="w-4 h-4 text-teal-600 dark:text-teal-400" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Email</p>
              <p className="text-xs text-gray-600 dark:text-gray-400"><EMAIL></p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-teal-200/50 dark:border-teal-700/50">
            <Clock className="w-4 h-4 text-teal-600 dark:text-teal-400" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Response Time</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">2-4 hours</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-teal-200/50 dark:border-teal-700/50">
            <Zap className="w-4 h-4 text-teal-600 dark:text-teal-400" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Priority Support</p>
              <p className="text-xs text-gray-600 dark:text-gray-400">Available for Pro users</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Card */}
      <Card className="border-green-200/30 dark:border-green-700/20">
        <CardHeader className="border-b border-green-200/30 dark:border-green-700/20 bg-gradient-to-r from-green-50/50 via-white to-teal-50/50 dark:from-green-950/20 dark:via-gray-800/50 dark:to-teal-950/20 rounded-t-3xl p-4">
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-gradient-to-br from-green-100 to-teal-100 dark:from-green-900/40 dark:to-teal-900/40">
              <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <CardTitle className="text-lg font-bold text-gray-900 dark:text-gray-100">System Status</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="p-4 bg-gradient-to-br from-white via-green-50/20 to-teal-50/20 dark:from-gray-800/50 dark:via-green-950/10 dark:to-teal-950/10">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">All systems operational</span>
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">Last updated: 2 minutes ago</p>
        </CardContent>
      </Card>
    </div>
  );
}

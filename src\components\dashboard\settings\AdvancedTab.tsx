'use client';

import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';

const AdvancedTab = () => {

  return (
    <div className="space-y-4">
      <Card className="border-purple-200/30 dark:border-purple-700/20">
        <CardHeader className="border-b border-purple-200/30 dark:border-purple-700/20 bg-gradient-to-r from-purple-50/50 via-white to-teal-50/50 dark:from-purple-950/20 dark:via-gray-800/50 dark:to-teal-950/20 rounded-t-3xl p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-purple-100 to-teal-100 dark:from-purple-900/40 dark:to-teal-900/40 shadow-sm">
              <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div>
              <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Advanced Settings</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
                Manage advanced application settings and preferences
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6 bg-gradient-to-br from-white via-purple-50/20 to-teal-50/20 dark:from-gray-800/50 dark:via-purple-950/10 dark:to-teal-950/10 space-y-6">
          <div className="space-y-6">
            <div className="bg-gradient-to-br from-white/50 via-purple-50/30 to-teal-50/30 dark:from-gray-800/30 dark:via-purple-950/20 dark:to-teal-950/20 rounded-xl border border-purple-200/30 dark:border-purple-700/20 p-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="p-1.5 rounded-lg bg-gradient-to-br from-purple-100 to-teal-100 dark:from-purple-900/40 dark:to-teal-900/40">
                  <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Data Preferences</h3>
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-purple-200/50 dark:border-purple-700/50">
                  <div className="space-y-1">
                    <Label htmlFor="data-export" className="text-sm font-medium text-gray-900 dark:text-gray-100">Export All Data</Label>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Download a copy of all your account data and invoices
                    </p>
                  </div>
                  <button className="bg-gradient-to-r from-purple-500 to-teal-600 hover:from-purple-600 hover:to-teal-700 text-white px-4 py-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 text-sm font-medium cursor-pointer">
                    Export Data
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Removed Developer Options section as per request */}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdvancedTab;

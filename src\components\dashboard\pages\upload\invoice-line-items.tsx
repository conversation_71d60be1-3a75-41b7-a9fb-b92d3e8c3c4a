"use client";

import { useState, useEffect } from "react";
import type { InvoiceData, LineItem } from "@/types/invoice";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Trash2 } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface InvoiceLineItemsProps {
  data: InvoiceData & { id?: string };
  editedData: InvoiceData & { id?: string };
  isEditing: boolean;
  onUpdate: (data: InvoiceData & { id?: string }) => void;
}

// Helper function to render nested object fields for line items
function renderObjectFields(obj: Record<string, unknown>) {
  return Object.entries(obj).map(([key, value]) => {
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      return (
        <div key={key} className="ml-2">
          <span className="block text-xs text-muted-foreground capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
          {renderObjectFields(value as Record<string, unknown>)}
        </div>
      );
    } else if (Array.isArray(value)) {
      return (
        <div key={key} className="ml-2">
          <span className="block text-xs text-muted-foreground capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
          <span className="text-xs">{JSON.stringify(value)}</span>
        </div>
      );
    } else if (value !== undefined && value !== null && value !== "") {
      return (
        <div key={key}>
          <span className="block text-xs text-muted-foreground capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
          <span className="text-xs">{String(value)}</span>
        </div>
      );
    }
    return null;
  });
}

// Helper function to safely convert value to string for input fields
function valueToString(value: unknown): string {
  if (value === null || value === undefined) return "";
  if (typeof value === 'object') return JSON.stringify(value);
  return String(value);
}

// Helper function to safely convert string input to appropriate type
function stringToValue(str: string, existingValue: unknown): unknown {
  if (str === "") return null;
  
  // If existing value was a number, try to convert to number
  if (typeof existingValue === 'number') {
    const num = parseFloat(str);
    return isNaN(num) ? str : num;
  }
  
  // If it looks like a number and we're dealing with quantity/price fields
  if (/^\d*\.?\d+$/.test(str)) {
    const num = parseFloat(str);
    return isNaN(num) ? str : num;
  }
  
  return str;
}

export function InvoiceLineItems({
  data,
  editedData,
  isEditing,
  onUpdate
}: InvoiceLineItemsProps) {
  const [lineItemColumns, setLineItemColumns] = useState<string[]>([]);

  // Extract all unique column names from line items
  useEffect(() => {
    // Ensure lineItems is an array
    const lineItems = Array.isArray(data.lineItems) ? data.lineItems : [];

    if (lineItems.length > 0) {
      const allColumns = new Set<string>();
      lineItems.forEach((item) => {
        Object.keys(item).forEach((key) => allColumns.add(key));
      });
      setLineItemColumns(Array.from(allColumns));
    } else {
      // Set default columns if no line items exist
      setLineItemColumns(['description', 'quantity', 'unitPrice', 'amount']);
    }
  }, [data.lineItems]);

  const addLineItem = () => {
    const newItem: LineItem = {};
    
    // Initialize with default values based on standard fields
    const standardFields = {
      description: "",
      quantity: null,
      unitPrice: null,
      totalPrice: null,
      amount: null,
      taxRate: null,
      discount: null,
      productSku: null,
      notes: null
    };

    // Add standard fields first
    Object.assign(newItem, standardFields);
    
    // Then add any custom columns from existing data
    lineItemColumns.forEach((col) => {
      if (!(col in standardFields)) {
        newItem[col] = "";
      }
    });

    // Ensure lineItems is an array before adding to it
    const currentLineItems = Array.isArray(editedData.lineItems) ? editedData.lineItems : [];

    const updatedData = {
      ...editedData,
      lineItems: [...currentLineItems, newItem],
    };

    onUpdate(updatedData);
  };

  const removeLineItem = (index: number) => {
    // Ensure lineItems is an array before filtering
    const currentLineItems = Array.isArray(editedData.lineItems) ? editedData.lineItems : [];

    const updatedData = {
      ...editedData,
      lineItems: currentLineItems.filter((_, i) => i !== index),
    };

    onUpdate(updatedData);
  };

  // Ensure lineItems is an array
  const currentData = isEditing ? editedData : data;
  const lineItems = Array.isArray(currentData.lineItems) ? currentData.lineItems : [];

  return (
    <div className="space-y-4">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50 dark:bg-slate-900/50">
              {lineItemColumns.map((column) => (
                <TableHead key={column} className="capitalize font-semibold text-slate-700 dark:text-slate-300">
                  {column.replace(/([A-Z])/g, " $1").trim()}
                </TableHead>
              ))}
              {isEditing && (
                <TableHead className="w-[100px]">Actions</TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {lineItems.length > 0 ? (
              lineItems.map((item, index) => (
                <TableRow key={index} className="hover:bg-slate-50/50 dark:hover:bg-slate-900/20">
                  {lineItemColumns.map((column) => (
                    <TableCell key={`${index}-${column}`}>
                      {isEditing ? (
                        <Input
                          value={valueToString(item[column])}
                          onChange={(e) => {
                            // Update the line item with proper type conversion
                            const currentLineItems = Array.isArray(editedData.lineItems) ? editedData.lineItems : [];
                            const updatedLineItems = [...currentLineItems];
                            if (updatedLineItems[index]) {
                              updatedLineItems[index] = {
                                ...updatedLineItems[index],
                                [column]: stringToValue(e.target.value, item[column])
                              };
                              onUpdate({
                                ...editedData,
                                lineItems: updatedLineItems
                              });
                            }
                          }}
                          className="border-slate-200 dark:border-slate-700"
                        />
                      ) : (
                        <span className="text-sm">
                          {typeof item[column] === 'object' && item[column] !== null ? renderObjectFields(item[column] as Record<string, unknown>) : valueToString(item[column]) || "N/A"}
                        </span>
                      )}
                    </TableCell>
                  ))}
                  {isEditing && (
                    <TableCell>
                      <Button
                        type="button"
                        variant="subtle"
                        size="icon"
                        onClick={() => removeLineItem(index)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  )}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={lineItemColumns.length + (isEditing ? 1 : 0)} className="text-center py-4 text-muted-foreground">
                  No line items found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {isEditing && (
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addLineItem}
          className="mt-4 border-slate-200 dark:border-slate-700"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Line Item
        </Button>
      )}
    </div>
  );
}

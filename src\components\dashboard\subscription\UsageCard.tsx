'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import {
  FileText,
  MessageSquare,
  CreditCard,
} from 'lucide-react';

interface UsageStats {
  invoiceUsage: number;
  invoiceLimit: number;
  chatUsage: number;
  chatLimit: number;
  daysUntilReset: number;
  resetDate: Date;
}

interface UsageCardProps {
  currentPlan: any;
  usageStats: UsageStats | null;
  usageLoading: boolean;
  documentsPercentage: number;
  chatPercentage: number;
}

const UsageCard: React.FC<UsageCardProps> = ({
  currentPlan,
  usageStats,
  usageLoading,
  documentsPercentage,
  chatPercentage,
}) => {
  if (!currentPlan) return null;

  return (
    <Card className="border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-br from-white via-teal-50/20 to-emerald-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-emerald-950/10">
      <CardHeader className="border-b border-teal-200/30 dark:border-teal-700/20 bg-gradient-to-r from-teal-50/50 via-white to-emerald-50/50 dark:from-teal-950/20 dark:via-gray-800/50 dark:to-emerald-950/20 rounded-t-3xl">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-xl bg-gradient-to-br from-teal-100 to-emerald-100 dark:from-teal-900/40 dark:to-emerald-900/40 shadow-sm">
            <FileText className="w-5 h-5 text-teal-600 dark:text-teal-400" />
          </div>
          <div>
            <CardTitle className="text-xl font-bold text-gray-900 dark:text-gray-100">Usage</CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400 font-medium">
              Your current usage for this billing period
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6 bg-gradient-to-br from-white via-teal-50/20 to-emerald-50/20 dark:from-gray-800/50 dark:via-teal-950/10 dark:to-emerald-950/10 space-y-6">
        {usageLoading ? (
          <div className="space-y-4">
            <div className="animate-pulse space-y-3">
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
            <div className="animate-pulse space-y-3">
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        ) : usageStats ? (
          <>
            <div className="p-4 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-teal-200/50 dark:border-teal-700/50 space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400 flex items-center gap-2 font-medium">
                  <FileText className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                  Documents processed
                </span>
                <span className="font-semibold text-gray-900 dark:text-gray-100">
                  {usageStats.invoiceUsage} /{' '}
                  {usageStats.invoiceLimit}
                </span>
              </div>
              <Progress
                value={documentsPercentage}
                className="h-3 bg-gray-200 dark:bg-gray-700"
              />
              <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 font-medium">
                <span>
                  {Math.round(documentsPercentage)}% used
                </span>
                <span>
                  Resets in {usageStats.daysUntilReset} days
                </span>
              </div>
            </div>

            <div className="p-4 bg-white/70 dark:bg-gray-800/70 rounded-lg border border-teal-200/50 dark:border-teal-700/50 space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400 flex items-center gap-2 font-medium">
                  <MessageSquare className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                  Chat messages used
                </span>
                <span className="font-semibold text-gray-900 dark:text-gray-100">
                  {usageStats.chatUsage} /{' '}
                  {usageStats.chatLimit}
                </span>
              </div>
              <Progress
                value={chatPercentage}
                className="h-3 bg-gray-200 dark:bg-gray-700"
              />
              <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 font-medium">
                <span>
                  {Math.round(chatPercentage)}% used
                </span>
                <span>
                  Resets on{' '}
                  {usageStats.resetDate.toLocaleDateString()}
                </span>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <div className="flex flex-col items-center gap-4">
              <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-700">
                <FileText className="w-6 h-6 text-gray-400" />
              </div>
              <p className="text-gray-600 dark:text-gray-400 font-medium">
                No usage data available
              </p>
            </div>
          </div>
        )}

        {!currentPlan && (
          <Button
            variant="outline"
            className="w-full bg-gradient-to-r from-teal-50 to-emerald-50 dark:from-teal-950/30 dark:to-emerald-950/30 border-teal-200/50 dark:border-teal-700/50 hover:from-teal-100 hover:to-emerald-100 dark:hover:from-teal-950/50 dark:hover:to-emerald-950/50 text-teal-700 dark:text-teal-300"
            onClick={() => {}}
            disabled={false}
          >
            <CreditCard className="mr-2 h-4 w-4" />
            Manage Payment Details
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default UsageCard;
